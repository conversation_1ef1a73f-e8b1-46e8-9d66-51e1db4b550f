﻿if (typeof joint === 'undefined') {
    alert('JointJS未加载成功，请检查CDN或网络！');
    throw new Error('JointJS未加载成功');
  }

  // 获取节点相关的所有连接线（包括内部节点之间的连接线）
  function getAllRelatedLinks(element) {
    const graph = element.graph;
    if (!graph) return [];

    let links = [];

    // 如果是容器节点，获取所有嵌套节点
    if (element.isContainer) {
      const embeddedCells = element.getEmbeddedCells();
      const embeddedElements = embeddedCells.filter(cell => !cell.isLink());

      // 获取容器内每个节点的连接线
      embeddedElements.forEach(cell => {
        // 获取与该节点相连的所有连接线
        const cellLinks = graph.getConnectedLinks(cell, { includeEnclosed: true });
        links = links.concat(cellLinks);
      });

      // 特别处理：获取容器内部节点之间的连接线
      // 这是关键部分，确保容器内部节点之间的连接线也被包含
      const internalLinks = [];
      embeddedElements.forEach(sourceCell => {
        embeddedElements.forEach(targetCell => {
          if (sourceCell.id !== targetCell.id) {
            // 查找从sourceCell到targetCell的连接线
            const directLinks = graph.getConnectedLinks(sourceCell, {
              outbound: true,
              inbound: false,
              includeEnclosed: true
            }).filter(link => {
              const target = link.getTargetCell();
              return target && target.id === targetCell.id;
            });

            internalLinks.push(...directLinks);
          }
        });
      });

      links = links.concat(internalLinks);
    }

    // 获取与元素本身相连的连接线
    const elementLinks = graph.getConnectedLinks(element);
    links = links.concat(elementLinks);

    // 去重
    return Array.from(new Set(links));
  }

  const graph = new joint.dia.Graph();
  // 计算初始画布尺寸
  const paperContainer = document.getElementById('paper-container');
  const initialWidth = window.innerWidth - 140; // 减去左侧面板宽度
  const initialHeight = window.innerHeight;

  const paper = new joint.dia.Paper({
    el: paperContainer,
    model: graph,
    width: initialWidth,
    height: initialHeight,
    gridSize: 10,
    drawGrid: true,
    background: { color: '#f8f9fa' },
    defaultLink: () => new joint.shapes.standard.Link({
        attrs: {
            line: {
                stroke: '#333',
                strokeWidth: 2,
                targetMarker: {
                    type: 'path',
                    d: 'M 10 -5 0 0 10 5 z',
                    fill: '#333'
                }
            }
        }
    }),
    interactive: {
        magnet: true,
        elementMove: true,
        linkMove: true,
        addLinkFromMagnet: true,
        validateConnection: function(cellViewS, magnetS, cellViewT, magnetT, end, linkView) {
            // Allow connections between any elements
            return true;
        }
    },
    highlighting: {
        'default': {
            name: 'stroke',
            options: {
                padding: 6,
                attrs: {
                    'stroke-width': 3,
                    stroke: '#1976d2'
                }
            }
        }
    },
    // 设置画布可平移
    async: true,
    frozen: false
  });

  // 添加全局层级管理 - 监听节点添加事件
  graph.on('add', function(cell) {
    // 如果添加的是节点（不是连接线）
    if (!cell.isLink()) {
      // 检查是否放置在容器节点上
      const containers = graph.getElements().filter(e => e.isContainer);
      for (const container of containers) {
        const bbox = container.getBBox();
        const cellBBox = cell.getBBox();
        // 判断节点中心是否在容器内
        const center = cellBBox.center();
        if (
          center.x > bbox.x &&
          center.x < bbox.x + bbox.width &&
          center.y > bbox.y &&
          center.y < bbox.y + bbox.height
        ) {
          // 检查节点类型，开始和结束节点不能被嵌套
          const nodeType = cell.get('type');
          const isStartOrEnd = nodeType === 'standard.Circle' &&
                              (cell.attr('label/text') === '开始' || cell.attr('label/text') === '结束');

          if (isStartOrEnd) {
            // 开始或结束节点不嵌套，但确保它们在最上层
            cell.toFront();
            continue;
          }

          // 如果节点不是容器节点本身，则嵌套
          if (!cell.isContainer) {
            // 确保节点在容器上方显示
            cell.toFront();

            // 嵌套节点到容器中
            container.embed(cell);

            // 确保嵌套后节点仍然可见
            setTimeout(() => {
              cell.toFront();
            }, 50);
          }
        }
      }
    }
  });

  // Ensure all elements are visible and interactive
  paper.on('cell:pointerdown', function(cellView) {
    const model = cellView.model;

    // 如果是连接线，检查它是否连接了容器内的节点
    if (model.isLink()) {
      const sourceCell = model.getSourceCell();
      const targetCell = model.getTargetCell();

      // 检查源节点或目标节点是否在容器内
      const sourceParent = sourceCell ? sourceCell.getParentCell() : null;
      const targetParent = targetCell ? targetCell.getParentCell() : null;

      // 如果连接线连接的是容器内的节点，确保它在最上层
      if (sourceParent && sourceParent.isContainer || targetParent && targetParent.isContainer) {
        // 找到所有相关的容器
        const containers = [];
        if (sourceParent && sourceParent.isContainer) containers.push(sourceParent);
        if (targetParent && targetParent.isContainer) containers.push(targetParent);

        // 确保容器、容器内节点和连接线都在最上层
        containers.forEach(container => {
          // 先将容器节点移到前面
          container.toFront({ deep: false });

          // 然后将嵌套节点移到前面
          const embeds = container.getEmbeddedCells();
          if (embeds.length > 0) {
            embeds.forEach(embed => {
              embed.toFront();
            });
          }
        });

        // 最后将连接线移到最前面
        model.toFront();
        return;
      }
    }

    // 对于其他元素，正常处理
    model.toFront();
  });

  // ========== 左侧面板拖放功能 ========== //
  // 获取左侧面板元素
  const sidebar = document.getElementById('sidebar');
  const nodeTypes = sidebar.querySelectorAll('.node-type');

  // 移除旧的palette元素（如果存在）
  const oldPalette = document.querySelector('.palette-item');
  if (oldPalette && oldPalette.parentElement) {
    oldPalette.parentElement.remove();
  }

  // 为每个节点类型添加draggable属性
  nodeTypes.forEach(nodeType => {
    nodeType.setAttribute('draggable', 'true');
  });

  // 添加一些额外的样式
  const style = document.createElement('style');
  style.innerHTML = `
    /* 拖拽时的样式 */
    .node-type.dragging {
      opacity: 0.5;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    /* 拖拽时的光标样式 */
    .node-type:active {
      cursor: grabbing;
    }

    /* 确保所有元素可见 */
    .joint-element {
      z-index: 1;
    }

    /* 确保开始和结束节点始终可见 */
    .joint-element circle {
      z-index: 2;
    }

    /* 确保文本标签可见 */
    .joint-element text {
      z-index: 3;
    }

    /* 平移模式样式 */
    .panning-cursor * {
      cursor: grab !important;
    }
    .panning-cursor:active * {
      cursor: grabbing !important;
    }
  `;
  document.head.appendChild(style);

  // ========== 锚点分组配置 ========== //
  const portGroups = {
    bottom: {
        position: { name: 'bottom' }, // 使用name: 'bottom'确保位置正确
        attrs: {
            circle: {
                r: 4, // 锚点半径设置为4px，直径8px
                magnet: true,
                stroke: '#333', // 边框颜色
                strokeWidth: 1,
                fill: '#fff', // 填充颜色
                cursor: 'crosshair',
                opacity: 0 // 默认隐藏
            }
        },
        markup: [
            {
                tagName: 'circle',
                selector: 'circle',
                attributes: {
                    'r': 4,
                    'magnet': 'true',
                    'fill': '#fff',
                    'stroke': '#333',
                    'stroke-width': 1
                }
            }
        ]
    },
    // 为 Switch 节点添加自定义锚点分组
    switchPorts: {
        position: function(ports, elBBox) {
            // 计算每个锚点的位置
            if (ports.length === 1) {
                // 如果只有一个锚点，放在中间
                return [{ x: elBBox.width / 2, y: elBBox.height }];
            } else if (ports.length > 1) {
                // 如果有多个锚点，均匀分布
                const spacing = elBBox.width / (ports.length + 1);
                return ports.map((port, index) => {
                    return {
                        x: spacing * (index + 1),
                        y: elBBox.height
                    };
                });
            }
            return [];
        },
        attrs: {
            circle: {
                r: 4,
                magnet: true,
                stroke: '#333',
                strokeWidth: 1,
                fill: '#fff',
                cursor: 'crosshair',
                opacity: 0
            }
        },
        markup: [
            {
                tagName: 'circle',
                selector: 'circle',
                attributes: {
                    'r': 4,
                    'magnet': 'true',
                    'fill': '#fff',
                    'stroke': '#333',
                    'stroke-width': 1
                }
            }
        ]
    }
  };

  // ========== 拖拽创建节点 ========== //
  let dragType = null;
  let draggedElement = null;

  // 为每个节点类型添加拖拽事件
  nodeTypes.forEach(nodeType => {
    // 拖拽开始
    nodeType.addEventListener('dragstart', e => {
      dragType = e.target.dataset.type;
      draggedElement = e.target;

      // 添加拖拽中的样式
      e.target.classList.add('dragging');

      // 设置拖拽数据
      e.dataTransfer.setData('text/plain', dragType);
      e.dataTransfer.effectAllowed = 'copy'; // 允许复制效果

      // 使用默认拖拽图像，不需要自定义
    });

    // 拖拽结束
    nodeType.addEventListener('dragend', e => {
      e.target.classList.remove('dragging');
      dragType = null;
      draggedElement = null;
    });
  });

  // 画布上的拖拽事件
  paper.el.addEventListener('dragover', e => {
    // 只有从侧边栏拖拽时才允许放置
    if (dragType) {
      e.preventDefault(); // 允许放置
      e.dataTransfer.dropEffect = 'copy'; // 设置放置效果为复制
    }
  });

  paper.el.addEventListener('drop', e => {
    e.preventDefault(); // 阻止默认行为，处理自定义放置逻辑
    if (!dragType) return; // 如果不是从侧边栏拖拽来的，不处理

    const { left, top } = paper.el.getBoundingClientRect();
    const x = e.clientX - left;
    const y = e.clientY - top;
    let node;

    // 根据 dragType 创建不同类型的节点
    if (dragType === 'start') {
        node = new joint.shapes.standard.Circle();
        node.position(x - 40, y - 40);
        node.resize(80, 80);
        node.attr({
            body: {
                fill: '#4caf50',
                stroke: '#388e3c',
                strokeWidth: 3,
                pointerEvents: 'auto' // Ensure pointer events work
            },
            label: {
                text: '开始',
                fill: '#fff',
                fontWeight: 'bold',
                fontSize: 20,
                pointerEvents: 'auto' // Ensure pointer events work
            }
        });
        // Set z-index to ensure visibility
        node.set('z', 10);
    } else if (dragType === 'end') {
        node = new joint.shapes.standard.Circle();
        node.position(x - 40, y - 40);
        node.resize(80, 80);
        node.attr({
            body: {
                fill: '#f44336',
                stroke: '#b71c1c',
                strokeWidth: 3,
                pointerEvents: 'auto' // Ensure pointer events work
            },
            label: {
                text: '结束',
                fill: '#fff',
                fontWeight: 'bold',
                fontSize: 20,
                pointerEvents: 'auto' // Ensure pointer events work
            }
        });
        // Set z-index to ensure visibility
        node.set('z', 10);
    } else if (dragType === 'process') {
        node = new joint.shapes.standard.Rectangle();
        node.position(x - 60, y - 40);
        node.resize(120, 80);
        node.attr({
            body: {
                fill: '#2196f3',
                stroke: '#1565c0',
                strokeWidth: 3,
                rx: 10,
                ry: 10,
                pointerEvents: 'auto'
            },
            label: {
                text: 'Grouping',
                fill: '#fff',
                fontWeight: 'bold',
                fontSize: 18,
                pointerEvents: 'auto'
            }
        });
        // Set z-index to ensure visibility
        node.set('z', 5);
    } else if (dragType === 'decision') {
        node = new joint.shapes.standard.Polygon();
        node.position(x - 60, y - 60);
        node.resize(120, 120);
        node.attr({
            body: {
                fill: '#ffeb3b',
                stroke: '#fbc02d',
                strokeWidth: 3,
                refPoints: '50,0 100,60 50,120 0,60',
                pointerEvents: 'auto'
            },
            label: {
                text: '决策',
                fill: '#333',
                fontWeight: 'bold',
                fontSize: 18,
                pointerEvents: 'auto'
            }
        });
        // Set z-index to ensure visibility
        node.set('z', 5);
    } else if (dragType === 'switch') {
        node = new joint.shapes.standard.Polygon();
        node.position(x - 70, y - 40);
        node.resize(140, 80);

        // 定义八边形的点坐标 (矩形四角切角)
        const octagonPoints = '15,0 85,0 100,15 100,85 85,100 15,100 0,85 0,15';

        node.attr({
            body: {
                fill: '#9c27b0', // 紫色背景
                stroke: '#6a1b9a',
                strokeWidth: 3,
                refPoints: octagonPoints,
                pointerEvents: 'auto'
            },
            label: {
                text: 'Switch',
                fill: '#fff',
                fontWeight: 'bold',
                fontSize: 18,
                pointerEvents: 'auto'
            }
        });

        // 设置初始 cases 属性，包含一个不可删除的 Default case
        node.prop('properties', {
            name: 'Switch',
            description: '评估多个条件并根据结果继续执行',
            cases: [
                { name: 'Default', expression: '', isDefault: true },
                { name: 'Case 1', expression: '' }
            ]
        });

        // 标记为 Switch 节点
        node.isSwitch = true;

        // Set z-index to ensure visibility
        node.set('z', 5);
    } else if (dragType === 'container') {
        node = new joint.shapes.standard.Rectangle();
        node.position(x - 150, y - 120);
        node.resize(300, 240);
        node.attr({
            body: {
                fill: '#FFFFFF', // 纯白色背景
                stroke: '#CCCCCC', // 灰色边框
                strokeWidth: 1, // 细边框
                rx: 2, // 轻微圆角
                ry: 2,
                pointerEvents: 'auto'
            },
            label: {
                text: '容器',
                fill: '#666666', // 深灰色文字
                fontWeight: 'bold',
                fontSize: 14,
                refX: 10, // 左对齐
                refY: 10, // 顶部对齐
                textAnchor: 'start',
                textVerticalAnchor: 'top',
                pointerEvents: 'auto'
            }
        });

        // 标记为容器节点
        node.isContainer = true;
        node.isResizable = true; // 标记为可调整大小

        // 为容器节点添加锚点
        node.set('ports', { groups: portGroups });
        node.addPort({ group: 'bottom' }); // 添加底部锚点
    }



    // 添加节点和端口
    if (node) {
        // 如果是 Switch 节点，添加多个锚点
        if (node.isSwitch) {
            // 获取 cases 数量
            const cases = node.prop('properties').cases || [];
            const casesCount = cases.length;

            console.log('创建 Switch 节点，Cases 数量:', casesCount);

            // 设置端口分组配置
            node.set('ports', { groups: portGroups });

            // 为每个 case 添加一个锚点
            for (let i = 0; i < casesCount; i++) {
                const portId = `case_${i}`;
                console.log(`添加锚点 ${portId} 对应 Case: ${cases[i].name}`);
                node.addPort({
                    id: portId,
                    group: 'switchPorts', // 使用自定义的 switchPorts 分组
                    attrs: {
                        text: {
                            text: cases[i].name,
                            fill: '#333',
                            fontSize: 10,
                            textAnchor: 'middle',
                            yAlignment: 'bottom',
                            refY: 20
                        },
                        circle: {
                            fill: '#fff',
                            stroke: '#333',
                            r: 5, // 稍微增大锚点半径，使其更容易看到
                            opacity: 0
                        }
                    }
                });
            }
        }
        // 如果不是容器节点和Switch节点，添加锚点
        else if (!node.isContainer) {
            // 设置端口分组配置并添加底部锚点
            node.set('ports', { groups: portGroups }); // 使用 set 方法更新 ports 属性
            node.addPort({ group: 'bottom' }); // 添加底部锚点
        }

        // 添加节点到图表
        node.addTo(graph);

        // 检查是否放置在容器节点上
        const containers = graph.getElements().filter(e => e.isContainer);
        for (const container of containers) {
            const bbox = container.getBBox();
            const nodeBBox = node.getBBox();
            // 判断节点中心是否在容器内
            const center = nodeBBox.center();
            if (
                center.x > bbox.x &&
                center.x < bbox.x + bbox.width &&
                center.y > bbox.y &&
                center.y < bbox.y + bbox.height
            ) {
                // 检查节点类型，开始和结束节点不能被嵌套
                const nodeType = node.get('type');
                const isStartOrEnd = nodeType === 'standard.Circle' &&
                                    (node.attr('label/text') === '开始' || node.attr('label/text') === '结束');

                if (isStartOrEnd) {
                    // 开始或结束节点不嵌套，但确保它们在最上层
                    node.toFront();
                    continue;
                }

                // 确保节点在容器上方显示
                node.toFront();

                // 嵌套节点到容器中
                container.embed(node);

                // 自动调整节点到容器内部
                const minX = bbox.x + 10; // 添加内边距
                const maxX = bbox.x + bbox.width - nodeBBox.width - 10;
                const minY = bbox.y + 30; // 顶部留出更多空间给标题
                const maxY = bbox.y + bbox.height - nodeBBox.height - 10;
                node.position(
                    Math.max(minX, Math.min(nodeBBox.x, maxX)),
                    Math.max(minY, Math.min(nodeBBox.y, maxY))
                );

                // 确保嵌套后节点仍然可见
                setTimeout(() => {
                    node.toFront();

                    // 获取所有相关连接线并确保它们在最上层
                    const relatedLinks = graph.getConnectedLinks(node);
                    if (relatedLinks.length > 0) {
                        relatedLinks.forEach(link => {
                            link.toFront();
                        });
                    }
                }, 50);

                break; // 找到一个容器后就退出循环
            }
        }
    }

    // 节点已添加，不需要显示任何成功提示

    // 重置拖拽状态
    dragType = null;
  });

  // 当拖拽离开画布时的处理
  paper.el.addEventListener('dragleave', e => {
    // 这里不重置dragType，避免拖拽经过子元素时误触发
    // dragType会在drop或dragend事件中重置
  });

  // ========== 画布自适应和平移功能 ========== //
  // 获取平移模式指示器
  const panningIndicator = document.querySelector('.panning-mode-indicator');

  // 平移状态变量
  let isPanningMode = false;
  let isPanning = false;
  let lastClientX = 0;
  let lastClientY = 0;

  // 调整画布大小函数
  function resizePaper() {
    // 获取当前窗口尺寸
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 计算画布尺寸（减去左侧面板宽度）
    const paperWidth = windowWidth - 140; // 140px是左侧面板宽度
    const paperHeight = windowHeight;

    // 获取当前的平移和缩放
    const currentTranslate = paper.translate();
    const currentScale = paper.scale();

    // 设置画布尺寸
    paper.setDimensions(paperWidth, paperHeight);

    // 触发自定义事件，通知其他组件画布尺寸已更改
    paper.trigger('paper:resize', paperWidth, paperHeight);

    // 更新小地图视口
    if (typeof updateMinimapViewport === 'function') {
      updateMinimapViewport();
    }

    // 更新小地图尺寸
    if (typeof updateMinimapSize === 'function') {
      updateMinimapSize();
    }

    // 如果有元素，确保它们在视图内
    const elements = graph.getElements();
    if (elements.length > 0) {
      // 获取所有元素的边界框
      const bbox = graph.getBBox();
      if (bbox) {
        // 检查是否需要调整视图以显示所有元素
        const visibleRect = paper.getArea();
        const needsAdjustment =
          bbox.x < visibleRect.x ||
          bbox.y < visibleRect.y ||
          bbox.x + bbox.width > visibleRect.x + visibleRect.width ||
          bbox.y + bbox.height > visibleRect.y + visibleRect.height;

        if (needsAdjustment) {
          // 计算适当的平移以显示所有元素
          const tx = -bbox.x + 50; // 添加一些边距
          const ty = -bbox.y + 50;

          // 应用平移
          paper.translate(tx, ty);
        }
      }
    }
  }

  // 初始调整大小 - 使用requestAnimationFrame确保DOM已完全加载
  requestAnimationFrame(function() {
    resizePaper();
  });

  // 监听窗口大小变化 - 使用防抖处理以提高性能
  let resizeTimeout;
  window.addEventListener('resize', function() {
    // 清除之前的定时器
    clearTimeout(resizeTimeout);

    // 设置新的定时器，延迟执行以防止频繁调用
    resizeTimeout = setTimeout(function() {
      resizePaper();
    }, 100);
  });

  // 监听空格键按下事件 - 进入平移模式
  document.addEventListener('keydown', function(evt) {
    if (evt.code === 'Space' && !isPanningMode) {
      // 进入平移模式
      isPanningMode = true;
      document.body.classList.add('panning-cursor');
      panningIndicator.style.display = 'block';

      // 暂时禁用其他交互
      paper.setInteractivity(false);
    }
  });

  // 监听空格键释放事件 - 退出平移模式
  document.addEventListener('keyup', function(evt) {
    if (evt.code === 'Space' && isPanningMode) {
      // 退出平移模式
      isPanningMode = false;
      isPanning = false;
      document.body.classList.remove('panning-cursor');
      panningIndicator.style.display = 'none';

      // 恢复交互
      paper.setInteractivity(true);
    }
  });

  // 监听鼠标按下事件 - 开始平移
  paper.el.addEventListener('mousedown', function(evt) {
    if (isPanningMode) {
      isPanning = true;
      lastClientX = evt.clientX;
      lastClientY = evt.clientY;

      // 阻止默认行为和事件冒泡
      evt.preventDefault();
      evt.stopPropagation();
    }
  });

  // 监听鼠标移动事件 - 执行平移
  document.addEventListener('mousemove', function(evt) {
    if (isPanning) {
      // 计算鼠标移动距离
      const dx = evt.clientX - lastClientX;
      const dy = evt.clientY - lastClientY;

      // 更新最后位置
      lastClientX = evt.clientX;
      lastClientY = evt.clientY;

      // 获取当前视图转换
      const currentTranslate = paper.translate();

      // 应用平移
      paper.translate(currentTranslate.tx + dx, currentTranslate.ty + dy);

      // 阻止默认行为和事件冒泡
      evt.preventDefault();
      evt.stopPropagation();
    }
  });

  // 监听鼠标释放事件 - 结束平移
  document.addEventListener('mouseup', function(evt) {
    if (isPanning) {
      isPanning = false;

      // 阻止默认行为和事件冒泡
      evt.preventDefault();
      evt.stopPropagation();
    }
  });

  // 监听鼠标离开窗口事件 - 结束平移
  document.addEventListener('mouseleave', function() {
    if (isPanning) {
      isPanning = false;
    }
  });

  // ========== 小地图功能 ========== //
  // 创建小地图
  const minimapContainer = document.getElementById('minimap-container');
  const minimapScale = 0.15; // 小地图缩放比例

  // 创建小地图的 paper 实例
  const minimap = new joint.dia.Paper({
    el: minimapContainer,
    model: graph, // 使用相同的 graph 模型
    width: 220,
    height: 180,
    gridSize: 1,
    interactive: false, // 禁用交互
    background: { color: '#f8f9fa' },
    async: true,
    frozen: false,
    sorting: joint.dia.Paper.sorting.NONE, // 提高渲染性能
    viewport: function(view) {
      // 简化小地图上的元素显示
      return true; // 显示所有元素
    },
    // 缩放小地图
    scale: { x: minimapScale, y: minimapScale }
  });

  // 创建小地图标题
  const minimapTitle = document.createElement('div');
  minimapTitle.className = 'minimap-title';
  minimapTitle.textContent = '工作流概览';
  minimapContainer.appendChild(minimapTitle);

  // 创建视口指示器
  const minimapViewport = document.createElement('div');
  minimapViewport.className = 'minimap-viewport';
  minimapContainer.appendChild(minimapViewport);

  // 设置小地图初始透明度
  minimapContainer.style.opacity = '0.7';

  // 更新视口指示器位置和大小
  function updateMinimapViewport() {
    // 获取主画布的可视区域和内容区域
    const paperRect = paper.getContentBBox();
    const visibleRect = paper.getArea();

    // 获取当前的平移和缩放
    const currentTranslate = paper.translate();
    const currentScale = paper.scale();

    // 获取当前画布尺寸
    const paperWidth = paper.options.width;
    const paperHeight = paper.options.height;

    // 计算实际可见区域（考虑平移和缩放）
    const actualVisibleRect = {
      x: -currentTranslate.tx / currentScale.sx,
      y: -currentTranslate.ty / currentScale.sy,
      width: paperWidth / currentScale.sx,
      height: paperHeight / currentScale.sy
    };

    // 确保小地图能显示所有内容
    let contentWidth = 0;
    let contentHeight = 0;

    // 如果有元素，使用元素的边界框
    if (paperRect && paperRect.width && paperRect.height) {
      contentWidth = Math.max(paperRect.width, actualVisibleRect.width);
      contentHeight = Math.max(paperRect.height, actualVisibleRect.height);
    } else {
      // 如果没有元素，使用可视区域
      contentWidth = actualVisibleRect.width;
      contentHeight = actualVisibleRect.height;
    }

    // 获取小地图的当前缩放比例
    const minimapCurrentScale = minimap.scale();
    const effectiveMinimapScale = minimapCurrentScale.sx;

    // 计算视口指示器的位置和大小
    const minimapX = actualVisibleRect.x * effectiveMinimapScale;
    const minimapY = actualVisibleRect.y * effectiveMinimapScale;
    const minimapWidth = actualVisibleRect.width * effectiveMinimapScale;
    const minimapHeight = actualVisibleRect.height * effectiveMinimapScale;

    // 设置视口指示器的位置和大小
    minimapViewport.style.left = minimapX + 'px';
    minimapViewport.style.top = minimapY + 'px';
    minimapViewport.style.width = minimapWidth + 'px';
    minimapViewport.style.height = minimapHeight + 'px';

    // 确保小地图视口指示器不超出小地图边界
    const vpLeft = parseFloat(minimapViewport.style.left);
    const vpTop = parseFloat(minimapViewport.style.top);
    const vpWidth = parseFloat(minimapViewport.style.width);
    const vpHeight = parseFloat(minimapViewport.style.height);

    if (vpLeft < 0) minimapViewport.style.left = '0px';
    if (vpTop < 0) minimapViewport.style.top = '0px';
    if (vpLeft + vpWidth > minimap.options.width) {
      minimapViewport.style.width = (minimap.options.width - vpLeft) + 'px';
    }
    if (vpTop + vpHeight > minimap.options.height) {
      minimapViewport.style.height = (minimap.options.height - vpTop) + 'px';
    }
  }

  // 初始更新视口指示器
  updateMinimapViewport();

  // 监听主画布的变化，更新视口指示器
  paper.on('translate', updateMinimapViewport);
  paper.on('scale', updateMinimapViewport);
  paper.on('resize', updateMinimapViewport);

  // 监听小地图上的视口指示器拖动
  let isDraggingViewport = false;
  let lastMinimapX = 0;
  let lastMinimapY = 0;

  // 视口指示器拖动开始
  minimapViewport.addEventListener('mousedown', function(evt) {
    isDraggingViewport = true;
    lastMinimapX = evt.clientX;
    lastMinimapY = evt.clientY;

    // 阻止事件冒泡和默认行为
    evt.stopPropagation();
    evt.preventDefault();
  });

  // 视口指示器拖动
  document.addEventListener('mousemove', function(evt) {
    if (!isDraggingViewport) return;

    // 计算鼠标移动距离
    const dx = evt.clientX - lastMinimapX;
    const dy = evt.clientY - lastMinimapY;

    // 更新最后位置
    lastMinimapX = evt.clientX;
    lastMinimapY = evt.clientY;

    // 计算主画布应该平移的距离（考虑缩放比例）
    const paperDx = dx / minimapScale;
    const paperDy = dy / minimapScale;

    // 获取当前主画布的平移位置
    const currentTranslate = paper.translate();

    // 应用平移到主画布
    paper.translate(currentTranslate.tx - paperDx, currentTranslate.ty - paperDy);

    // 阻止事件冒泡和默认行为
    evt.stopPropagation();
    evt.preventDefault();
  });

  // 视口指示器拖动结束
  document.addEventListener('mouseup', function() {
    isDraggingViewport = false;
  });

  // 调整小地图内容以适应所有元素
  function fitMinimapContent() {
    // 获取所有元素的边界框
    const elements = graph.getElements();

    // 如果没有元素，使用默认缩放
    if (elements.length === 0) {
      minimap.scale(minimapScale, minimapScale);
      updateMinimapViewport();
      return;
    }

    // 计算所有元素的总边界框
    const bbox = graph.getBBox();
    if (!bbox) {
      minimap.scale(minimapScale, minimapScale);
      updateMinimapViewport();
      return;
    }

    // 获取主画布的可视区域
    const visibleRect = paper.getArea();

    // 添加一些边距
    const padding = 20;
    const expandedBBox = {
      x: Math.min(bbox.x, visibleRect.x) - padding,
      y: Math.min(bbox.y, visibleRect.y) - padding,
      width: Math.max(bbox.width, visibleRect.width) + padding * 2,
      height: Math.max(bbox.height, visibleRect.height) + padding * 2
    };

    // 计算适当的缩放比例
    const scaleX = minimap.options.width / expandedBBox.width;
    const scaleY = minimap.options.height / expandedBBox.height;
    const scale = Math.min(scaleX, scaleY, 0.15); // 不超过0.15的缩放

    // 应用新的缩放比例
    minimap.scale(scale, scale);

    // 更新视口指示器
    updateMinimapViewport();
  }

  // 监听元素添加和删除事件，更新小地图
  graph.on('add remove change:position change:size', function(cell) {
    // 延迟更新，避免频繁更新
    clearTimeout(this._minimapUpdateTimeout);
    this._minimapUpdateTimeout = setTimeout(function() {
      updateMinimapViewport();

      // 如果是添加或删除操作，考虑调整小地图缩放
      if (cell && (!cell.previous('position') || !cell.previous('size'))) {
        fitMinimapContent();
      }
    }, 50);
  });

  // 更新小地图尺寸和位置的函数
  function updateMinimapSize() {
    // 调整小地图大小
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 在小屏幕上缩小小地图
    if (windowWidth < 768) {
      minimap.setDimensions(180, 150);
      minimapContainer.style.width = '180px';
      minimapContainer.style.height = '150px';
    } else {
      minimap.setDimensions(220, 180);
      minimapContainer.style.width = '220px';
      minimapContainer.style.height = '180px';
    }

    // 确保小地图位置正确（右下角）
    minimapContainer.style.right = '20px';
    minimapContainer.style.bottom = '20px';

    // 更新小地图视口
    updateMinimapViewport();

    // 重新适应内容
    fitMinimapContent();
  }

  // 初始化小地图尺寸
  updateMinimapSize();

  // 监听窗口大小变化，更新小地图 - 使用防抖处理
  let minimapResizeTimeout;
  window.addEventListener('resize', function() {
    // 清除之前的定时器
    clearTimeout(minimapResizeTimeout);

    // 设置新的定时器，延迟执行以防止频繁调用
    minimapResizeTimeout = setTimeout(function() {
      updateMinimapSize();
    }, 100);
  });

  // 监听小地图容器的点击事件，实现直接跳转
  minimapContainer.addEventListener('mousedown', function(evt) {
    // 如果点击的是视口指示器，不处理
    if (evt.target === minimapViewport) return;

    // 获取点击位置相对于小地图的坐标
    const minimapRect = minimapContainer.getBoundingClientRect();
    const clickX = evt.clientX - minimapRect.left;
    const clickY = evt.clientY - minimapRect.top;

    // 计算对应主画布的坐标（考虑缩放比例）
    const paperX = clickX / minimapScale;
    const paperY = clickY / minimapScale;

    // 获取主画布的可视区域大小
    const visibleRect = paper.getArea();

    // 计算新的平移位置（使点击位置居中）
    const newTx = -paperX + visibleRect.width / 2;
    const newTy = -paperY + visibleRect.height / 2;

    // 应用平移到主画布
    paper.translate(newTx, newTy);

    // 阻止事件冒泡和默认行为
    evt.stopPropagation();
    evt.preventDefault();
  });

  // ========== 删除连接线 ========== //
  // 存储当前选中的连接线
  let selectedLink = null;

  // 为连接线添加工具按钮
  paper.options.linkTools = {
    'remove': true  // 启用删除工具
  };

  // 创建连接线工具视图
  const linkTools = new joint.dia.ToolsView({
    tools: [
      new joint.linkTools.Remove({
        distance: '50%',  // 在连接线中间位置
        markup: [{
          tagName: 'circle',
          selector: 'button',
          attributes: {
            'r': 10,
            'fill': '#000',
            'cursor': 'pointer'
          }
        }, {
          tagName: 'path',
          selector: 'icon',
          attributes: {
            'd': 'M -4 -4 L 4 4 M -4 4 L 4 -4',
            'stroke': '#fff',
            'stroke-width': 2
          }
        }]
      })
    ]
  });

  // 点击连接线时选中它并显示删除工具
  paper.on('link:pointerclick', function(linkView, evt) {
    // 阻止默认的双击编辑行为
    evt.stopPropagation();

    console.log('Link clicked:', linkView.model.id);

    // 如果按住Ctrl键点击，则直接删除连接线
    if (evt.ctrlKey || evt.metaKey) {
      linkView.model.remove();
      selectedLink = null;
      return;
    }

    // 如果点击的是当前选中的连接线，取消选中
    if (selectedLink === linkView.model) {
      // 取消选中
      selectedLink.attr('line/stroke', '#333');
      selectedLink.attr('line/strokeWidth', 2);
      selectedLink = null;

      // 移除工具
      linkView.removeTools();
      return;
    }

    // 如果之前有选中的连接线，取消它的选中状态
    if (selectedLink) {
      selectedLink.attr('line/stroke', '#333');
      selectedLink.attr('line/strokeWidth', 2);

      // 移除之前连接线的工具
      const prevLinkView = paper.findViewByModel(selectedLink);
      if (prevLinkView) {
        prevLinkView.removeTools();
      }
    }

    // 选中当前连接线
    selectedLink = linkView.model;
    selectedLink.attr('line/stroke', '#ff4081');
    selectedLink.attr('line/strokeWidth', 3);

    // 显示工具
    linkView.addTools(linkTools);
  });

  // 点击空白处取消选中
  paper.on('blank:pointerclick', function() {
    if (selectedLink) {
      // 取消选中状态
      selectedLink.attr('line/stroke', '#333');
      selectedLink.attr('line/strokeWidth', 2);

      // 移除工具
      const linkView = paper.findViewByModel(selectedLink);
      if (linkView) {
        linkView.removeTools();
      }

      selectedLink = null;
    }
  });

  // 监听键盘事件
  document.addEventListener('keydown', function(evt) {
    if ((evt.key === 'Delete' || evt.key === 'Backspace') && selectedLink) {
      selectedLink.remove();
      selectedLink = null;
    }
  });

  // 添加连接线悬停提示样式
  const customStyles = document.createElement('style');
  customStyles.innerHTML = `
    /* 连接线样式 */
    .joint-link {
      cursor: pointer;
    }
    .joint-link:hover {
      stroke-width: 3px;
    }

    /* 连接线删除工具样式 */
    .joint-tool-remove circle {
      fill: black;
      stroke: none;
      cursor: pointer;
      transition: transform 0.2s;
    }
    .joint-tool-remove path {
      stroke: white;
      stroke-width: 2;
      pointer-events: none;
    }
    .joint-tool-remove:hover circle {
      transform: scale(1.2);
    }

    /* 节点删除图标样式 */
    .node-delete-icon {
      position: absolute;
      border-radius: 50%;
      background-color: black;
      box-shadow: 0 0 5px rgba(0,0,0,0.5);
      cursor: pointer;
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.2s ease;
      pointer-events: all !important;
    }
    .node-delete-icon:hover {
      transform: scale(1.2) !important;
    }

    /* 节点属性图标样式 */
    .node-property-icon {
      position: absolute;
      border-radius: 4px;
      background-color: rgba(128, 128, 128, 0.6); /* 修改为半透明灰色 */
      box-shadow: 0 0 5px rgba(0,0,0,0.5);
      cursor: pointer;
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.2s ease;
      pointer-events: all !important;
      height: 10px !important; /* 降低高度 */
    }
    .node-property-icon:hover {
      transform: scale(1.2) !important;
    }
    .node-property-icon .dot {
      width: 3px;
      height: 3px;
      border-radius: 50%;
      background-color: white;
      margin: 0 1px;
    }

    /* 确保删除图标在最上层 */
    .joint-tools {
      z-index: 10000;
    }

    /* 属性面板样式 */
    .property-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.2);
      padding: 20px;
      z-index: 20000;
      min-width: 300px;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      display: none;
    }

    .property-panel h3 {
      margin-top: 0;
      margin-bottom: 0;
      color: #333;
      font-size: 18px;
      position: relative;
    }

    .panel-header {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    .panel-header h3 {
      margin-bottom: 0;
      flex: 1;
    }

    .panel-close-icon {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #aaa;
      font-size: 20px;
      font-weight: normal;
      transition: color 0.2s ease;
      border-radius: 50%;
      margin-left: 10px;
    }

    .panel-close-icon:hover {
      color: #666;
    }

    .property-panel .form-group {
      margin-bottom: 15px;
    }

    .property-panel label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }

    .property-panel input[type="text"],
    .property-panel input[type="number"],
    .property-panel select,
    .property-panel textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 14px;
    }

    .property-panel textarea {
      min-height: 80px;
      resize: vertical;
    }

    .property-panel .button-group {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }

    .property-panel button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-left: 10px;
    }

    .property-panel .save-btn {
      background-color: #2196f3;
      color: white;
    }

    .property-panel .save-btn:hover {
      background-color: #1976d2;
    }

    .property-panel .cancel-btn {
      background-color: #f5f5f5;
      color: #333;
    }

    .property-panel .cancel-btn:hover {
      background-color: #e0e0e0;
    }

    /* 选项卡样式 */
    .tabs-container {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 20px;
      margin-top: 5px;
    }

    .tab {
      padding: 10px 15px;
      cursor: pointer;
      font-weight: 500;
      color: #666;
      position: relative;
      transition: all 0.2s ease;
    }

    .tab:hover {
      color: #2196f3;
    }

    .tab.active {
      color: #2196f3;
    }

    .tab.active::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #2196f3;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    /* Switch 节点 Cases 容器样式 */
    #cases-container {
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 10px;
      background-color: #f9f9f9;
    }

    #cases-container .case-item {
      background-color: white;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    #cases-container .case-item:last-child {
      margin-bottom: 0;
    }

    /* 滚动条样式 */
    #cases-container::-webkit-scrollbar {
      width: 8px;
    }

    #cases-container::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    #cases-container::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 4px;
    }

    #cases-container::-webkit-scrollbar-thumb:hover {
      background: #aaa;
    }

    /* 容器节点样式 */
    .joint-element.container {
      cursor: move;
    }

    /* 容器节点悬停效果 */
    .joint-element.container:hover rect {
      stroke-width: 1px;
      stroke: #999999;
    }

    /* 容器节点调整大小时的样式 */
    .resize-handle {
      position: absolute;
      width: 8px;
      height: 8px;
      background-color: rgba(0, 0, 0, 0.7);
      border: 2px solid white;
      z-index: 10000;
      box-shadow: 0 0 3px rgba(0,0,0,0.3);
      transition: transform 0.1s ease;
    }

    .resize-handle:hover {
      transform: scale(1.2) !important;
      background-color: rgba(0, 0, 0, 0.9);
    }

    .resize-handle.nw {
      cursor: nwse-resize;
    }

    .resize-handle.se {
      cursor: nwse-resize;
    }

    .resize-handle.n {
      cursor: ns-resize;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
    }

    .resize-handle.e {
      cursor: ew-resize;
      top: 50%;
      right: -4px;
      transform: translateY(-50%);
    }

    .resize-handle.s {
      cursor: ns-resize;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
    }

    .resize-handle.w {
      cursor: ew-resize;
      top: 50%;
      left: -4px;
      transform: translateY(-50%);
    }

    .resize-handle.nw {
      cursor: nwse-resize;
      top: -4px;
      left: -4px;
    }

    .resize-handle.ne {
      cursor: nesw-resize;
      top: -4px;
      right: -4px;
    }

    .resize-handle.se {
      cursor: nwse-resize;
      bottom: -4px;
      right: -4px;
    }

    .resize-handle.sw {
      cursor: nesw-resize;
      bottom: -4px;
      left: -4px;
    }

    /* 锚点样式 */
    .joint-port {
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: auto !important;
    }

    .joint-element:hover .joint-port {
      opacity: 1;
    }

    .joint-port circle {
      fill: #fff;
      stroke: #333;
      stroke-width: 1;
      r: 4;
      pointer-events: auto !important;
    }

    .joint-port:hover circle {
      fill: #f5f5f5;
      stroke: #1976d2;
      r: 5;
    }

    /* 确保所有元素可交互 */
    .joint-element {
      pointer-events: auto !important;
    }

    /* 确保所有SVG元素可见 */
    svg.joint-paper > g {
      pointer-events: auto !important;
    }

    /* 确保所有元素有正确的z-index */
    .joint-cell {
      z-index: auto !important;
    }

    /* 确保容器内的节点可见 */
    .joint-element.joint-type-standard-rectangle {
      pointer-events: auto !important;
    }

    /* 确保锚点在最上层 */
    .joint-port {
      z-index: 1000 !important;
    }

    /* 确保删除图标在最上层 */
    .node-delete-icon {
      z-index: 2000 !important;
    }
  `;
  document.head.appendChild(customStyles);

  // ========== 容器节点大小调整功能 ========== //
  // 存储当前正在调整大小的容器节点和调整方向
  let resizingContainer = null;
  let resizeDirection = null;
  let resizeHandles = [];
  let initialSize = null;
  let initialPosition = null;
  let initialMousePosition = null;

  // 创建调整大小的句柄
  function createResizeHandles(containerView) {
    // 移除旧的调整句柄
    removeResizeHandles();

    const container = containerView.model;
    if (!container.isResizable) return;

    const position = container.position();
    const size = container.size();
    const paperRect = paper.el.getBoundingClientRect();

    // 只创建左上角和右下角的调整大小句柄
    const directions = ['nw', 'se'];

    directions.forEach(direction => {
      const handle = document.createElement('div');
      handle.className = `resize-handle ${direction}`;
      handle.setAttribute('data-direction', direction);

      // Create triangular shape using CSS clip-path
      if (direction === 'nw') {
        // Top-left triangle pointing toward top-left (↖)
        handle.style.clipPath = 'polygon(0% 0%, 100% 0%, 0% 100%)';
      } else if (direction === 'se') {
        // Bottom-right triangle pointing toward bottom-right (↘)
        handle.style.clipPath = 'polygon(100% 0%, 100% 100%, 0% 100%)';
      }

      // 设置句柄位置
      const handleRect = calculateHandlePosition(direction, position, size, paperRect);

      // 获取句柄大小
      const handleSize = 8; // 句柄大小为8px

      // 调整位置，使句柄中心点与容器角重叠
      handle.style.left = `${handleRect.left - handleSize/2}px`;
      handle.style.top = `${handleRect.top - handleSize/2}px`;

      // 添加鼠标进入事件，防止句柄消失
      handle.addEventListener('mouseenter', function(evt) {
        handle._isMouseOver = true;
      });

      // 添加鼠标离开事件
      handle.addEventListener('mouseleave', function(evt) {
        handle._isMouseOver = false;
      });

      // 添加鼠标按下事件
      handle.addEventListener('mousedown', function(evt) {
        evt.stopPropagation();

        // 开始调整大小
        resizingContainer = container;
        resizeDirection = direction;
        initialSize = { width: size.width, height: size.height };
        initialPosition = { x: position.x, y: position.y };
        initialMousePosition = { x: evt.clientX, y: evt.clientY };

        // 添加鼠标移动和松开事件
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
      });

      // 将句柄添加到DOM
      paper.el.appendChild(handle);
      resizeHandles.push(handle);
    });
  }

  // 计算句柄位置
  function calculateHandlePosition(direction, position, size, paperRect) {
    // 将模型坐标转换为屏幕坐标
    const svgPoint = paper.svg.createSVGPoint();

    let x, y;

    switch(direction) {
      case 'n':
        x = position.x + size.width / 2;
        y = position.y;
        break;
      case 'e':
        x = position.x + size.width;
        y = position.y + size.height / 2;
        break;
      case 's':
        x = position.x + size.width / 2;
        y = position.y + size.height;
        break;
      case 'w':
        x = position.x;
        y = position.y + size.height / 2;
        break;
      case 'nw':
        // 左上角，使图标中心点与容器角重叠
        x = position.x;
        y = position.y;
        break;
      case 'ne':
        x = position.x + size.width;
        y = position.y;
        break;
      case 'se':
        // 右下角，使图标中心点与容器角重叠
        x = position.x + size.width;
        y = position.y + size.height;
        break;
      case 'sw':
        x = position.x;
        y = position.y + size.height;
        break;
    }

    // 确保坐标有效
    if (x === undefined || y === undefined) {
      console.error('Invalid coordinates for resize handle:', direction, position, size);
      return { left: 0, top: 0 };
    }

    svgPoint.x = x;
    svgPoint.y = y;

    const screenPoint = svgPoint.matrixTransform(paper.svg.getScreenCTM());

    return {
      left: screenPoint.x - paperRect.left,
      top: screenPoint.y - paperRect.top
    };
  }

  // 处理调整大小
  function handleResize(evt) {
    if (!resizingContainer) return;

    const dx = evt.clientX - initialMousePosition.x;
    const dy = evt.clientY - initialMousePosition.y;

    // 计算新的大小和位置
    let newWidth = initialSize.width;
    let newHeight = initialSize.height;
    let newX = initialPosition.x;
    let newY = initialPosition.y;

    // 根据调整方向更新大小和位置
    if (resizeDirection.includes('e')) {
      newWidth = Math.max(100, initialSize.width + dx);
    }
    if (resizeDirection.includes('w')) {
      const widthChange = Math.min(initialSize.width - 100, dx);
      newWidth = initialSize.width - widthChange;
      newX = initialPosition.x + widthChange;
    }
    if (resizeDirection.includes('s')) {
      newHeight = Math.max(80, initialSize.height + dy);
    }
    if (resizeDirection.includes('n')) {
      const heightChange = Math.min(initialSize.height - 80, dy);
      newHeight = initialSize.height - heightChange;
      newY = initialPosition.y + heightChange;
    }

    // 更新容器大小和位置
    resizingContainer.resize(newWidth, newHeight);
    resizingContainer.position(newX, newY);

    // 更新调整句柄位置
    updateResizeHandles();
  }

  // 停止调整大小
  function stopResize() {
    resizingContainer = null;
    resizeDirection = null;
    initialSize = null;
    initialPosition = null;
    initialMousePosition = null;

    // 移除事件监听器
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
  }

  // 更新调整句柄位置
  function updateResizeHandles() {
    if (!resizingContainer) return;

    const containerView = paper.findViewByModel(resizingContainer);
    if (!containerView) return;

    // 重新创建调整句柄
    createResizeHandles(containerView);
  }

  // 移除调整句柄
  function removeResizeHandles() {
    // 清理已知的调整句柄
    resizeHandles.forEach(handle => {
      if (handle.parentNode) {
        handle.parentNode.removeChild(handle);
      }
    });
    resizeHandles = [];

    // 额外清理：查找并移除所有可能残留的调整句柄
    const allResizeHandles = document.querySelectorAll('.resize-handle');
    if (allResizeHandles.length > 0) {
      console.log(`发现 ${allResizeHandles.length} 个残留的调整句柄，强制清理`);
      allResizeHandles.forEach(handle => {
        if (handle.parentNode) {
          handle.parentNode.removeChild(handle);
        }
      });
    }

    // 重置调整大小相关的状态变量
    resizingContainer = null;
    resizeDirection = null;
    initialSize = null;
    initialPosition = null;
    initialMousePosition = null;
  }

  // 这部分代码将被删除，因为我们会合并到下面的鼠标事件处理程序中

  // ========== 节点删除和属性功能 ========== //
  // 存储当前悬停的节点和图标
  let hoveredElement = null;
  let nodeDeleteIcon = null;
  let nodePropertyIcon = null;
  let hideIconTimeout = null; // 用于延迟隐藏图标的定时器
  let propertyPanel = null; // 属性面板
  let currentEditingNode = null; // 当前正在编辑属性的节点

  // 创建删除图标的函数
  function createNodeDeleteIcon(elementView) {
    // 移除旧的图标（如果存在）
    removeNodeDeleteIcon();

    // 获取节点的位置和大小
    const element = elementView.model;
    const position = element.position();
    const size = element.size();

    // 创建一个DOM元素作为删除图标
    const iconSize = 16;
    const iconElement = document.createElement('div');
    iconElement.className = 'node-delete-icon';
    iconElement.style.position = 'fixed'; // 使用fixed定位，避免滚动问题
    iconElement.style.width = `${iconSize}px`;
    iconElement.style.height = `${iconSize}px`;
    iconElement.style.borderRadius = '50%';
    iconElement.style.backgroundColor = 'black';
    iconElement.style.boxShadow = '0 0 5px rgba(0,0,0,0.5)';
    iconElement.style.cursor = 'pointer';
    iconElement.style.zIndex = '20000'; // 提高z-index确保在最上层
    iconElement.style.display = 'flex';
    iconElement.style.justifyContent = 'center';
    iconElement.style.alignItems = 'center';
    iconElement.style.transition = 'transform 0.2s ease';
    iconElement.style.pointerEvents = 'auto'; // 确保图标可点击

    // 为特定节点类型添加数据属性，用于识别
    const nodeType = element.get('type');
    const nodeLabel = element.attr('label/text');
    if (nodeType === 'standard.Circle' && nodeLabel === '结束') {
      iconElement.dataset.nodeType = 'end';
    } else if (element.isContainer) {
      iconElement.dataset.nodeType = 'container';
    }

    // 添加X形状
    iconElement.innerHTML = `
      <svg width="10" height="10" viewBox="0 0 10 10" style="overflow:visible;">
        <path d="M2 2 L8 8 M2 8 L8 2" stroke="white" stroke-width="2"/>
      </svg>
    `;

    // 添加悬停效果和鼠标状态跟踪
    iconElement.onmouseover = function() {
      this.style.transform = 'scale(1.2)';
      this._isMouseOver = true; // 标记鼠标在图标上

      // 清除任何现有的延迟隐藏定时器
      if (hideIconTimeout) {
        clearTimeout(hideIconTimeout);
        hideIconTimeout = null;
      }
    };
    iconElement.onmouseout = function() {
      this.style.transform = 'scale(1)';
      this._isMouseOver = false; // 标记鼠标不在图标上

      // 如果鼠标不在节点上，延迟隐藏图标
      if (!hoveredElement) {
        delayHideNodeDeleteIcon();
      }
    };

    // 添加点击事件 - 改进版本，确保可靠的节点删除
    iconElement.onclick = function(evt) {
      // 阻止事件冒泡和默认行为
      evt.stopPropagation();
      evt.preventDefault();

      // 立即禁用图标，防止多次点击
      this.style.pointerEvents = 'none';
      this.style.opacity = '0.5';

      // 保存当前悬停的节点引用，防止在删除过程中被修改
      const elementToDelete = hoveredElement;

      if (elementToDelete) {
        console.log('删除节点:', elementToDelete.id, elementToDelete.get('type'));

        // 立即清除所有图标，防止干扰
        removeAllRelatedIcons();

        // 立即清除悬停状态，防止状态混乱
        hoveredElement = null;

        // 确保移除resize句柄
        removeResizeHandles();

        // 隐藏属性面板
        hidePropertyPanel();

        // 检查节点类型，对特定类型使用增强的删除逻辑
        const nodeType = elementToDelete.get('type');
        const nodeLabel = elementToDelete.attr('label/text');
        const isEndNode = nodeType === 'standard.Circle' && nodeLabel === '结束';
        const isStartNode = nodeType === 'standard.Circle' && nodeLabel === '开始';
        const isContainerNode = elementToDelete.isContainer;

        // 使用增强的删除逻辑
        deleteNodeWithFallback(elementToDelete, isEndNode, isContainerNode, isStartNode);
      }
    };

    // 获取paper元素的位置
    const paperRect = paper.el.getBoundingClientRect();

    // 计算图标在页面中的位置（右上角，更靠近节点）
    let iconX, iconY;

    // 检查节点类型，为不同形状的节点调整位置
    const elementType = element.get('type');

    if (elementType === 'standard.Polygon') {
      // 对于菱形节点（决策节点），调整位置
      iconX = position.x + size.width * 0.7; // 向左移动，更靠近节点中心
      iconY = position.y + size.height * 0.3; // 向下移动，更靠近节点中心
    } else {
      // 对于其他节点，使用默认位置
      iconX = position.x + size.width - 5; // 向左移动，更靠近节点
      iconY = position.y + 5; // 向下移动，更靠近节点
    }

    // 将SVG坐标转换为页面坐标
    const svgPoint = paper.svg.createSVGPoint();
    svgPoint.x = iconX;
    svgPoint.y = iconY;
    const screenPoint = svgPoint.matrixTransform(paper.svg.getScreenCTM());

    // 设置图标位置
    iconElement.style.left = `${screenPoint.x - iconSize/2}px`;
    iconElement.style.top = `${screenPoint.y - iconSize/2}px`;

    // 将图标添加到body中
    document.body.appendChild(iconElement);

    // 保存图标引用
    nodeDeleteIcon = iconElement;

    return nodeDeleteIcon;
  }

  // 辅助函数：使用多层次的删除策略确保节点被删除
  function deleteNodeWithFallback(node, isEndNode, isContainerNode, isStartNode) {
    try {
      // 如果没有传入isStartNode参数，检查是否为开始节点
      if (isStartNode === undefined) {
        const nodeType = node.get('type');
        const nodeLabel = node.attr('label/text');
        isStartNode = nodeType === 'standard.Circle' && nodeLabel === '开始';
      }

      // 对于容器节点，需要特殊处理
      if (isContainerNode) {
        deleteContainerNode(node);
      }
      // 对于结束节点，使用增强的删除逻辑
      else if (isEndNode) {
        deleteEndNode(node);
      }
      // 对于开始节点，使用增强的删除逻辑
      else if (isStartNode) {
        deleteStartNode(node);
      }
      // 对于普通节点，直接删除
      else {
        node.remove();
      }
    } catch (error) {
      console.error('删除节点时出错:', error);

      // 备用方法1 - 使用graph API删除
      try {
        console.log('尝试使用备用方法1删除节点');
        graph.removeCells([node]);
      } catch (e) {
        console.error('备用方法1失败:', e);

        // 备用方法2 - 使用更底层的API
        try {
          console.log('尝试使用备用方法2删除节点');
          if (node.collection) {
            node.collection.remove(node);
          }
        } catch (e2) {
          console.error('备用方法2也失败:', e2);
        }
      }
    } finally {
      // 无论成功与否，确保清理所有相关元素
      setTimeout(() => {
        removeAllRelatedIcons();
        removeResizeHandles();
      }, 100);
    }
  }

  // 辅助函数：专门处理容器节点的删除
  function deleteContainerNode(containerNode) {
    console.log(`容器节点 ${containerNode.id} 将被删除`);

    // 获取容器中的所有嵌套节点
    const embeddedCells = containerNode.getEmbeddedCells ? containerNode.getEmbeddedCells() : [];
    console.log(`容器节点包含 ${embeddedCells.length} 个嵌套节点`);

    // 先解除所有嵌套关系
    if (embeddedCells.length > 0) {
      embeddedCells.forEach(cell => {
        try {
          containerNode.unembed(cell);
          console.log(`解除嵌套关系: ${cell.id}`);
        } catch (e) {
          console.warn(`解除嵌套关系失败: ${cell.id}`, e);
        }
      });
    }

    // 使用多种方法尝试删除容器节点
    try {
      // 方法1: 直接使用remove方法
      containerNode.remove();
      console.log('容器节点删除成功 (方法1)');
    } catch (e1) {
      console.warn('容器节点删除方法1失败:', e1);

      try {
        // 方法2: 使用graph API
        graph.removeCells([containerNode]);
        console.log('容器节点删除成功 (方法2)');
      } catch (e2) {
        console.warn('容器节点删除方法2失败:', e2);

        // 方法3: 使用底层collection API
        if (containerNode.collection) {
          containerNode.collection.remove(containerNode);
          console.log('容器节点删除成功 (方法3)');
        } else {
          throw new Error('无法删除容器节点');
        }
      }
    }
  }

  // 移除删除图标的函数
  function removeNodeDeleteIcon() {
    // 清除任何现有的延迟隐藏定时器
    if (hideIconTimeout) {
      clearTimeout(hideIconTimeout);
      hideIconTimeout = null;
    }

    if (nodeDeleteIcon && nodeDeleteIcon.parentNode) {
      nodeDeleteIcon.parentNode.removeChild(nodeDeleteIcon);
      nodeDeleteIcon = null;
    }
  }

  // 创建属性图标的函数
  function createNodePropertyIcon(elementView) {
    // 移除旧的图标（如果存在）
    removeNodePropertyIcon();

    // 获取节点的位置和大小
    const element = elementView.model;

    // 检查是否为开始或结束节点，如果是则不显示属性图标
    const nodeType = element.get('type');
    const nodeLabel = element.attr('label/text');
    if (nodeType === 'standard.Circle' && (nodeLabel === '开始' || nodeLabel === '结束')) {
      return null;
    }

    const position = element.position();
    const size = element.size();

    // 创建一个DOM元素作为属性图标
    const iconSize = 16;
    const iconHeight = 10; // 降低高度
    const iconElement = document.createElement('div');
    iconElement.className = 'node-property-icon';
    iconElement.style.position = 'fixed'; // 使用fixed定位，避免滚动问题
    iconElement.style.width = `${iconSize}px`;
    iconElement.style.height = `${iconHeight}px`;
    iconElement.style.borderRadius = '4px';
    iconElement.style.backgroundColor = 'black';
    iconElement.style.boxShadow = '0 0 5px rgba(0,0,0,0.5)';
    iconElement.style.cursor = 'pointer';
    iconElement.style.zIndex = '10000';
    iconElement.style.display = 'flex';
    iconElement.style.justifyContent = 'center';
    iconElement.style.alignItems = 'center';
    iconElement.style.transition = 'transform 0.2s ease';
    iconElement.style.pointerEvents = 'auto'; // 确保图标可点击

    // 添加三个点图标
    iconElement.innerHTML = `
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
    `;

    // 添加悬停效果和鼠标状态跟踪
    iconElement.onmouseover = function() {
      this.style.transform = 'scale(1.2)';
      this._isMouseOver = true; // 标记鼠标在图标上

      // 清除任何现有的延迟隐藏定时器
      if (hideIconTimeout) {
        clearTimeout(hideIconTimeout);
        hideIconTimeout = null;
      }
    };
    iconElement.onmouseout = function() {
      this.style.transform = 'scale(1)';
      this._isMouseOver = false; // 标记鼠标不在图标上

      // 如果鼠标不在节点上，延迟隐藏图标
      if (!hoveredElement) {
        delayHideIcons();
      }
    };

    // 添加点击事件
    iconElement.onclick = function(evt) {
      evt.stopPropagation();
      evt.preventDefault();
      console.log('属性图标被点击');
      if (hoveredElement) {
        // 显示属性面板
        showPropertyPanel(hoveredElement);
      }
    };

    // 获取paper元素的位置
    const paperRect = paper.el.getBoundingClientRect();

    // 计算图标在页面中的位置（顶部中间，增加向下偏移）
    let iconX, iconY;

    // 对于所有节点类型，图标位置都在顶部中间
    iconX = position.x + size.width / 2;

    // 根据节点类型和大小动态计算偏移量
    let yOffset = 10; // 默认偏移10px

    // 对于菱形节点（决策节点），增加偏移量
    if (nodeType === 'standard.Polygon') {
      yOffset = Math.max(15, size.height * 0.15); // 至少15px或节点高度的15%
    }
    // 对于容器节点，使用固定偏移
    else if (element.isContainer) {
      yOffset = 10; // 固定为10px，与普通节点一致
    }
    // 对于普通矩形节点，使用默认偏移

    iconY = position.y + yOffset;

    // 将SVG坐标转换为页面坐标
    const svgPoint = paper.svg.createSVGPoint();
    svgPoint.x = iconX;
    svgPoint.y = iconY;
    const screenPoint = svgPoint.matrixTransform(paper.svg.getScreenCTM());

    // 设置图标位置
    iconElement.style.left = `${screenPoint.x - iconSize/2}px`;
    iconElement.style.top = `${screenPoint.y - iconHeight/2}px`;

    // 将图标添加到body中
    document.body.appendChild(iconElement);

    // 保存图标引用
    nodePropertyIcon = iconElement;

    return nodePropertyIcon;
  }

  // 移除属性图标的函数
  function removeNodePropertyIcon() {
    if (nodePropertyIcon && nodePropertyIcon.parentNode) {
      nodePropertyIcon.parentNode.removeChild(nodePropertyIcon);
      nodePropertyIcon = null;
    }
  }

  // 移除所有与节点相关的图标（包括嵌套节点的图标）- 增强版本
  function removeAllRelatedIcons(node) {
    // 清除任何现有的延迟隐藏定时器
    if (hideIconTimeout) {
      clearTimeout(hideIconTimeout);
      hideIconTimeout = null;
    }

    // 重置图标引用变量
    nodeDeleteIcon = null;
    nodePropertyIcon = null;

    // 使用更可靠的方法移除所有图标
    function removeAllIconElements() {
      // 查找所有图标元素
      const allIcons = document.querySelectorAll('.node-delete-icon, .node-property-icon');

      // 如果找到图标，记录并移除它们
      if (allIcons.length > 0) {
        console.log(`移除 ${allIcons.length} 个图标元素`);
        allIcons.forEach(icon => {
          try {
            if (icon.parentNode) {
              // 禁用事件处理，防止在删除过程中触发事件
              icon.style.pointerEvents = 'none';
              icon.parentNode.removeChild(icon);
            }
          } catch (e) {
            console.warn('移除图标元素失败:', e);
          }
        });
        return true;
      }
      return false;
    }

    // 立即执行一次清理
    removeAllIconElements();

    // 如果提供了容器节点，确保其嵌套节点的图标也被移除
    if (node && node.isContainer) {
      try {
        const embeddedCells = node.getEmbeddedCells ? node.getEmbeddedCells() : [];
        if (embeddedCells && embeddedCells.length > 0) {
          console.log(`容器节点 ${node.id} 包含 ${embeddedCells.length} 个嵌套节点，清理它们的图标`);
        }
      } catch (e) {
        console.warn('获取嵌套节点失败:', e);
      }
    }

    // 特殊处理：如果是结束节点，记录日志
    if (node) {
      try {
        const nodeType = node.get('type');
        const nodeLabel = node.attr('label/text');

        if (nodeType === 'standard.Circle' && nodeLabel === '结束') {
          console.log(`清理结束节点 ${node.id} 的图标`);
        }
        // 特殊处理：如果是Grouping节点或Decision节点，记录日志
        else if (nodeType === 'standard.Rectangle' && !node.isContainer && !node.isSwitch) {
          console.log(`清理Grouping节点 ${node.id} 的图标`);
        } else if (nodeType === 'standard.Polygon') {
          console.log(`清理Decision节点 ${node.id} 的图标`);
        }
      } catch (e) {
        console.warn('获取节点类型失败:', e);
      }
    }

    // 使用多次延迟清理，确保所有图标都被移除
    // 第一次延迟清理
    setTimeout(() => {
      removeAllIconElements();

      // 第二次延迟清理，确保彻底
      setTimeout(() => {
        if (removeAllIconElements()) {
          // 如果第二次清理还发现图标，进行第三次清理
          setTimeout(removeAllIconElements, 50);
        }
      }, 50);
    }, 10);
  }

  // 延迟隐藏图标的函数 - 改进版本
  function delayHideIcons() {
    // 清除任何现有的延迟隐藏定时器
    if (hideIconTimeout) {
      clearTimeout(hideIconTimeout);
      hideIconTimeout = null;
    }

    // 设置新的延迟隐藏定时器
    hideIconTimeout = setTimeout(function() {
      // 检查鼠标是否在图标上
      const deleteIconHovered = nodeDeleteIcon && nodeDeleteIcon._isMouseOver;
      const propertyIconHovered = nodePropertyIcon && nodePropertyIcon._isMouseOver;

      if (deleteIconHovered || propertyIconHovered) {
        console.log('图标仍在鼠标悬停状态，不隐藏');
        return; // 如果鼠标在任一图标上，不隐藏
      }

      // 检查是否仍有悬停的节点
      if (hoveredElement) {
        // 如果仍有悬停的节点，不隐藏图标
        console.log('节点仍在悬停状态，不隐藏图标');
        return;
      }

      // 安全地移除图标
      if (nodeDeleteIcon) {
        console.log('延迟隐藏删除图标');
        removeNodeDeleteIcon();
      }

      if (nodePropertyIcon) {
        console.log('延迟隐藏属性图标');
        removeNodePropertyIcon();
      }

      hideIconTimeout = null;
    }, 300); // 300毫秒延迟
  }

  // 专门用于删除图标的延迟隐藏函数
  function delayHideNodeDeleteIcon() {
    // 清除任何现有的延迟隐藏定时器
    if (hideIconTimeout) {
      clearTimeout(hideIconTimeout);
      hideIconTimeout = null;
    }

    // 设置新的延迟隐藏定时器
    hideIconTimeout = setTimeout(function() {
      // 检查鼠标是否在删除图标上
      if (nodeDeleteIcon && nodeDeleteIcon._isMouseOver) {
        return; // 如果鼠标在删除图标上，不隐藏
      }

      // 检查是否仍有悬停的节点
      if (hoveredElement) {
        // 如果仍有悬停的节点，不隐藏图标
        return;
      }

      // 安全地移除删除图标
      removeNodeDeleteIcon();
      hideIconTimeout = null;
    }, 300); // 300毫秒延迟
  }

  // 创建属性面板
  function createPropertyPanel() {
    // 如果面板已存在，先移除旧的面板
    if (propertyPanel) {
      if (propertyPanel.parentNode) {
        propertyPanel.parentNode.removeChild(propertyPanel);
      }
      propertyPanel = null;
    }

    // 创建面板元素
    const panel = document.createElement('div');
    panel.className = 'property-panel';
    document.body.appendChild(panel);

    // 添加点击事件，防止点击面板时关闭面板
    panel.addEventListener('click', function(evt) {
      evt.stopPropagation();
    });

    // 移除旧的文档点击事件监听器
    if (window._propertyPanelClickHandler) {
      document.removeEventListener('click', window._propertyPanelClickHandler);
    }

    // 添加点击文档事件，点击面板外部时关闭面板
    window._propertyPanelClickHandler = function(evt) {
      if (propertyPanel && propertyPanel.style.display === 'block') {
        // 检查点击是否在面板外部
        if (!propertyPanel.contains(evt.target)) {
          hidePropertyPanel();
        }
      }
    };
    document.addEventListener('click', window._propertyPanelClickHandler);

    propertyPanel = panel;
    return panel;
  }

  // 显示属性面板
  function showPropertyPanel(element) {
    console.log('显示属性面板', element.id);

    // 保存当前编辑的节点
    currentEditingNode = element;

    // 获取或创建面板
    const panel = createPropertyPanel();

    // 根据节点类型生成不同的表单内容
    const nodeType = element.get('type');
    const nodeLabel = element.attr('label/text');

    // 获取节点已有的属性数据
    const properties = element.prop('properties') || {};

    let panelTitle = '';
    let commonContent = '';
    let propertiesContent = '';
    let useTabs = true; // 默认使用选项卡

    // 为不同类型的节点生成内容
    if (nodeType === 'standard.Circle' && (nodeLabel === '开始' || nodeLabel === '结束')) {
      // 开始/结束节点属性 - 不使用选项卡
      useTabs = false;
      panelTitle = nodeLabel === '开始' ? '开始节点属性' : '结束节点属性';
      commonContent = `
        <div class="form-group">
          <label for="node-name">节点名称</label>
          <input type="text" id="node-name" value="${properties.name || nodeLabel}" />
        </div>
        <div class="form-group">
          <label for="node-description">描述</label>
          <textarea id="node-description">${properties.description || ''}</textarea>
        </div>
      `;
    } else if (nodeType === 'standard.Rectangle' && !element.isContainer && !element.isSwitch) {
      // Grouping节点属性
      panelTitle = 'Grouping节点属性';

      // 通用选项卡内容
      commonContent = `
        <div class="form-group">
          <label for="node-name">节点名称</label>
          <input type="text" id="node-name" value="${properties.name || 'Grouping'}" />
        </div>
        <div class="form-group">
          <label for="node-description">描述</label>
          <textarea id="node-description">${properties.description || ''}</textarea>
        </div>
      `;

      // 属性选项卡内容
      propertiesContent = `
        <div class="form-group">
          <label for="node-field">Field</label>
          <input type="text" id="node-field" value="${properties.field || ''}" />
        </div>
        <div class="form-group">
          <label for="node-field-value">Field Value</label>
          <input type="text" id="node-field-value" value="${properties.fieldValue || ''}" />
        </div>
        <div class="form-group">
          <label for="node-wrapper-fields">Wrapper Fields</label>
          <input type="text" id="node-wrapper-fields" value="${properties.wrapperFields || ''}" />
        </div>
        <div class="form-group">
          <label for="node-category-value">Category Value</label>
          <input type="text" id="node-category-value" value="${properties.categoryValue || ''}" />
        </div>
      `;
    } else if (nodeType === 'standard.Polygon') {
      // 决策节点属性
      panelTitle = '决策节点属性';

      // 通用选项卡内容
      commonContent = `
        <div class="form-group">
          <label for="node-name">节点名称</label>
          <input type="text" id="node-name" value="${properties.name || '决策'}" />
        </div>
        <div class="form-group">
          <label for="node-description">描述</label>
          <textarea id="node-description">${properties.description || ''}</textarea>
        </div>
      `;

      // 属性选项卡内容
      propertiesContent = `
        <div class="form-group">
          <label for="node-condition">决策条件</label>
          <textarea id="node-condition">${properties.condition || ''}</textarea>
        </div>
      `;
    } else if (element.isSwitch) {
      // Switch 节点属性
      panelTitle = 'Switch 节点属性';

      // 获取 cases 数据
      const cases = properties.cases || [
        { name: 'Case 1', expression: '' },
        { name: 'Case 2', expression: '' }
      ];

      // 通用选项卡内容
      commonContent = `
        <div class="form-group">
          <label for="node-name">节点名称</label>
          <input type="text" id="node-name" value="${properties.name || 'Switch'}" />
        </div>
        <div class="form-group">
          <label for="node-description">描述</label>
          <textarea id="node-description">${properties.description || '评估多个条件并根据结果继续执行'}</textarea>
        </div>
      `;

      // 属性选项卡内容 - Cases
      propertiesContent = `
        <h4 style="margin-top: 0; border-bottom: 1px solid #eee; padding-bottom: 8px;">Cases</h4>
        <div id="cases-container" style="max-height: 300px; overflow-y: auto; padding-right: 5px;">
      `;

      // 生成 cases 表单
      cases.forEach((caseItem, index) => {
        // 检查是否为 Default case
        const isDefault = caseItem.isDefault === true;

        propertiesContent += `
          <div class="case-item" data-index="${index}" ${isDefault ? 'data-default="true"' : ''}>
            <div style="display: flex; margin-bottom: 10px; align-items: center;">
              <div style="flex: 1;">
                <label for="case-name-${index}">名称</label>
                <input type="text" id="case-name-${index}" value="${caseItem.name}" style="width: 100%; ${isDefault ? 'background-color: #f5f5f5;' : ''}" ${isDefault ? 'disabled' : ''} />
              </div>
              ${!isDefault ? `
              <button type="button" class="delete-case-btn" data-index="${index}" style="margin-left: 10px; background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                <span style="font-size: 16px;">&times;</span>
              </button>
              ` : ''}
            </div>
            ${!isDefault ? `
            <div>
              <label for="case-expression-${index}">表达式</label>
              <textarea id="case-expression-${index}" style="width: 100%; min-height: 60px;">${caseItem.expression}</textarea>
            </div>
            ` : `
            <div style="padding: 10px; background-color: #f9f9f9; border-radius: 4px; font-style: italic; color: #666;">
              Default Case 是默认执行路径，不需要表达式判断
            </div>
            `}
          </div>
        `;
      });

      // 添加 "添加 Case" 按钮
      propertiesContent += `
        </div>
        <button type="button" id="add-case-btn" style="background: #4CAF50; color: white; border: none; border-radius: 4px; padding: 8px 16px; margin-top: 10px; cursor: pointer; display: flex; align-items: center;">
          <span style="font-size: 18px; margin-right: 5px;">+</span> 添加 Case
        </button>
      `;
    } else if (element.isContainer) {
      // 容器节点属性
      panelTitle = '容器节点属性';

      // 通用选项卡内容
      commonContent = `
        <div class="form-group">
          <label for="node-name">容器名称</label>
          <input type="text" id="node-name" value="${properties.name || '容器'}" />
        </div>
        <div class="form-group">
          <label for="node-description">描述</label>
          <textarea id="node-description">${properties.description || ''}</textarea>
        </div>
      `;

      // 属性选项卡内容
      propertiesContent = `
        <div class="form-group">
          <label for="node-category">分类</label>
          <input type="text" id="node-category" value="${properties.category || ''}" />
        </div>
      `;
    }

    // 构建面板内容
    let panelContent = '';

    if (useTabs) {
      // 使用选项卡的面板内容
      panelContent = `
        <div class="tabs-container">
          <div class="tab active" data-tab="common">Common</div>
          <div class="tab" data-tab="properties">Properties</div>
        </div>
        <div class="tab-content active" id="tab-common">
          ${commonContent}
        </div>
        <div class="tab-content" id="tab-properties">
          ${propertiesContent}
        </div>
        <div class="button-group">
          <button class="cancel-btn" id="property-cancel">取消</button>
          <button class="save-btn" id="property-save">保存</button>
        </div>
      `;
    } else {
      // 不使用选项卡的面板内容（开始/结束节点）
      panelContent = `
        ${commonContent}
        <div class="button-group">
          <button class="cancel-btn" id="property-cancel">取消</button>
          <button class="save-btn" id="property-save">保存</button>
        </div>
      `;
    }

    // 设置面板内容
    panel.innerHTML = `
      <div class="panel-header">
        <h3>${panelTitle}</h3>
        <div class="panel-close-icon">&times;</div>
      </div>
      ${panelContent}
    `;

    // 显示面板
    panel.style.display = 'block';

    // 添加选项卡切换功能
    if (useTabs) {
      const tabs = panel.querySelectorAll('.tab');
      const tabContents = panel.querySelectorAll('.tab-content');

      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有选项卡的活动状态
          tabs.forEach(t => t.classList.remove('active'));
          tabContents.forEach(c => c.classList.remove('active'));

          // 添加当前选项卡的活动状态
          this.classList.add('active');
          const tabId = this.getAttribute('data-tab');
          document.getElementById(`tab-${tabId}`).classList.add('active');
        });
      });
    }

    // 添加按钮事件
    const saveBtn = document.getElementById('property-save');
    const cancelBtn = document.getElementById('property-cancel');
    const closeIcon = panel.querySelector('.panel-close-icon');

    // 移除旧的事件监听器（如果存在）
    if (saveBtn) {
      const newSaveBtn = saveBtn.cloneNode(true);
      saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
      newSaveBtn.addEventListener('click', saveNodeProperties);
    }

    if (cancelBtn) {
      const newCancelBtn = cancelBtn.cloneNode(true);
      cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
      newCancelBtn.addEventListener('click', hidePropertyPanel);
    }

    // 添加关闭图标事件
    if (closeIcon) {
      closeIcon.addEventListener('click', hidePropertyPanel);
    }

    // 如果是 Switch 节点，添加 Case 相关事件
    if (element.isSwitch) {
      // 添加 Case 按钮事件
      const addCaseBtn = document.getElementById('add-case-btn');
      if (addCaseBtn) {
        addCaseBtn.addEventListener('click', function() {
          const casesContainer = document.getElementById('cases-container');
          const caseItems = casesContainer.querySelectorAll('.case-item');
          const newIndex = caseItems.length;

          // 创建新的 Case 元素
          const newCaseElement = document.createElement('div');
          newCaseElement.className = 'case-item';
          newCaseElement.dataset.index = newIndex;
          newCaseElement.innerHTML = `
            <div style="display: flex; margin-bottom: 10px; align-items: center;">
              <div style="flex: 1;">
                <label for="case-name-${newIndex}">名称</label>
                <input type="text" id="case-name-${newIndex}" value="Case ${newIndex + 1}" style="width: 100%;" />
              </div>
              <button type="button" class="delete-case-btn" data-index="${newIndex}" style="margin-left: 10px; background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                <span style="font-size: 16px;">&times;</span>
              </button>
            </div>
            <div>
              <label for="case-expression-${newIndex}">表达式</label>
              <textarea id="case-expression-${newIndex}" style="width: 100%; min-height: 60px;"></textarea>
            </div>
          `;

          // 添加到容器
          casesContainer.appendChild(newCaseElement);

          // 添加删除按钮事件
          const deleteBtn = newCaseElement.querySelector('.delete-case-btn');
          deleteBtn.addEventListener('click', function() {
            // 检查是否为 Default case，Default case 不能删除
            if (newCaseElement.dataset.default === 'true') {
              alert('Default case 不能删除');
              return;
            }

            casesContainer.removeChild(newCaseElement);
          });
        });
      }

      // 为现有的删除 Case 按钮添加事件
      const deleteCaseBtns = document.querySelectorAll('.delete-case-btn');
      deleteCaseBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const index = this.dataset.index;
          const caseItem = document.querySelector(`.case-item[data-index="${index}"]`);
          if (caseItem) {
            // 检查是否为 Default case，Default case 不能删除
            if (caseItem.dataset.default === 'true') {
              alert('Default case 不能删除');
              return;
            }

            const casesContainer = document.getElementById('cases-container');
            casesContainer.removeChild(caseItem);
          }
        });
      });
    }
  }

  // 隐藏属性面板
  function hidePropertyPanel() {
    console.log('隐藏属性面板');
    if (propertyPanel) {
      propertyPanel.style.display = 'none';
    }
    currentEditingNode = null;
  }

  // 保存节点属性
  function saveNodeProperties() {
    if (!currentEditingNode) return;

    // 获取节点类型
    const nodeType = currentEditingNode.get('type');
    const nodeLabel = currentEditingNode.attr('label/text');

    // 创建属性对象
    const properties = {
      name: document.getElementById('node-name').value,
      description: document.getElementById('node-description').value
    };

    // 根据节点类型获取特定属性
    if (nodeType === 'standard.Rectangle' && !currentEditingNode.isContainer && !currentEditingNode.isSwitch) {
      // Grouping节点特有属性
      properties.field = document.getElementById('node-field').value;
      properties.fieldValue = document.getElementById('node-field-value').value;
      properties.wrapperFields = document.getElementById('node-wrapper-fields').value;
      properties.categoryValue = document.getElementById('node-category-value').value;
    } else if (nodeType === 'standard.Polygon') {
      // 决策节点特有属性
      properties.condition = document.getElementById('node-condition').value;
    } else if (currentEditingNode.isContainer) {
      // 容器节点特有属性
      properties.category = document.getElementById('node-category').value;
    } else if (currentEditingNode.isSwitch) {
      // Switch 节点特有属性 - 收集 cases
      const casesContainer = document.getElementById('cases-container');
      const caseItems = casesContainer.querySelectorAll('.case-item');
      const cases = [];

      caseItems.forEach((caseItem, index) => {
        const caseIndex = caseItem.dataset.index;
        const caseName = document.getElementById(`case-name-${caseIndex}`).value;
        const isDefault = caseItem.dataset.default === 'true';

        // 对于 Default case，表达式字段可能不存在，因为我们在UI中移除了它
        let caseExpression = '';
        if (!isDefault) {
          const expressionElement = document.getElementById(`case-expression-${caseIndex}`);
          if (expressionElement) {
            caseExpression = expressionElement.value;
          }
        }

        cases.push({
          name: caseName,
          expression: caseExpression,
          isDefault: isDefault
        });
      });

      // 确保至少有一个 Default case
      const hasDefault = cases.some(c => c.isDefault);
      if (!hasDefault && cases.length > 0) {
        // 如果没有 Default case，将第一个 case 设为 Default
        cases[0].name = 'Default';
        cases[0].isDefault = true;
      } else if (cases.length === 0) {
        // 如果没有任何 case，添加一个 Default case
        cases.push({
          name: 'Default',
          expression: '',
          isDefault: true
        });
      }

      properties.cases = cases;

      // 更新 Switch 节点的锚点
      updateSwitchPorts(currentEditingNode, cases);
    }

    // 保存属性到节点
    currentEditingNode.prop('properties', properties);

    // 更新节点显示的文本
    if (properties.name) {
      currentEditingNode.attr('label/text', properties.name);
    }

    // 隐藏面板
    hidePropertyPanel();
  }

  // 更新 Switch 节点的锚点
  function updateSwitchPorts(node, cases) {
    if (!node || !node.isSwitch) return;

    console.log('更新 Switch 节点锚点，Cases 数量:', cases.length);

    // 移除所有现有锚点
    const ports = node.getPorts();
    ports.forEach(port => {
      node.removePort(port.id);
    });

    // 为每个 case 添加一个锚点
    const casesCount = cases.length;
    for (let i = 0; i < casesCount; i++) {
      const portId = `case_${i}`;
      console.log(`添加锚点 ${portId} 对应 Case: ${cases[i].name}`);
      node.addPort({
        id: portId,
        group: 'switchPorts', // 使用自定义的 switchPorts 分组
        attrs: {
          text: {
            text: cases[i].name,
            fill: '#333',
            fontSize: 10,
            textAnchor: 'middle',
            yAlignment: 'bottom',
            refY: 20
          },
          circle: {
            fill: '#fff',
            stroke: '#333',
            r: 5, // 稍微增大锚点半径，使其更容易看到
            opacity: 0
          }
        }
      });
    }

    // 确保锚点可见
    setTimeout(() => {
      const elementView = paper.findViewByModel(node);
      if (elementView) {
        const ports = elementView.el.querySelectorAll('.joint-port');
        if (ports && ports.length > 0) {
          ports.forEach(port => {
            port.style.opacity = 1;
            port.style.visibility = 'visible';
          });
        }

        node.getPorts().forEach(port => {
          node.portProp(port.id, 'attrs/circle/opacity', 1);
        });
      }
    }, 100);
  }

  // 鼠标进入节点时显示删除图标、属性图标和调整句柄 - 改进版本
  paper.on('element:mouseover', function(elementView, evt) {
    // 清除任何现有的延迟隐藏定时器
    if (hideIconTimeout) {
      clearTimeout(hideIconTimeout);
      hideIconTimeout = null;
    }

    const element = elementView.model;

    // 检查节点是否有效
    if (!element || !element.id) {
      console.warn('无效的节点元素');
      return;
    }

    // 如果已经有悬停的节点，先清理它的图标
    if (hoveredElement && hoveredElement !== element) {
      // 移除旧节点的图标，但不重置hoveredElement
      removeNodeDeleteIcon();
      removeNodePropertyIcon();
    }

    // 保存当前悬停的节点
    hoveredElement = element;
    console.log('鼠标悬停在节点上:', element.id, element.get('type'));

    // 创建并显示删除图标（对所有节点类型都显示，包括开始和结束节点）
    createNodeDeleteIcon(elementView);

    // 创建并显示属性图标（对于开始和结束节点不显示）
    const nodeType = element.get('type');
    const nodeLabel = element.attr('label/text');
    if (!(nodeType === 'standard.Circle' && (nodeLabel === '开始' || nodeLabel === '结束'))) {
      createNodePropertyIcon(elementView);
    }

    // 如果是容器节点，显示调整大小的句柄
    if (element.isContainer && element.isResizable) {
      createResizeHandles(elementView);
    }

    // 确保锚点可见
    try {
      // 显示端口（锚点）
      const ports = elementView.el.querySelectorAll('.joint-port');
      if (ports && ports.length > 0) {
        ports.forEach(port => {
          port.style.opacity = 1;
          port.style.visibility = 'visible';
        });
      }

      // 另一种方式确保锚点可见
      element.getPorts().forEach(port => {
        element.portProp(port.id, 'attrs/circle/opacity', 1);
      });

      // 如果是容器节点，确保内部节点和连接线可见
      if (element.isContainer) {
        // 获取容器内的所有节点
        const embeds = element.getEmbeddedCells();
        const embeddedElements = embeds.filter(cell => !cell.isLink());

        // 获取所有相关连接线（包括内部节点之间的连接线）
        const relatedLinks = getAllRelatedLinks(element);

        // 先将容器节点移到前面
        element.toFront({ deep: false });

        // 然后将嵌套节点移到前面
        if (embeddedElements.length > 0) {
          embeddedElements.forEach(embed => {
            embed.toFront();
          });
        }

        // 最后将连接线移到最前面
        if (relatedLinks.length > 0) {
          relatedLinks.forEach(link => {
            link.toFront();
          });
        }

        // 延迟执行，再次确保连接线可见
        setTimeout(() => {
          if (relatedLinks.length > 0) {
            relatedLinks.forEach(link => {
              link.toFront();
            });
          }
        }, 50);
      } else {
        // 确保节点在最上层，这样锚点和删除图标才能正确显示
        element.toFront();
      }
    } catch (error) {
      console.log('Error showing ports:', error);
    }
  });

  // 鼠标离开节点时处理
  paper.on('element:mouseout', function(elementView, evt) {
    const element = elementView.model;

    // 检查鼠标是否真的离开了节点（不是移动到子元素上）
    const relatedTarget = evt.relatedTarget || evt.toElement;
    if (elementView.el.contains(relatedTarget)) {
      return; // 如果鼠标仍在节点内部，不做任何处理
    }

    // 检查鼠标是否移动到了删除图标上
    if (nodeDeleteIcon && (relatedTarget === nodeDeleteIcon || nodeDeleteIcon.contains(relatedTarget))) {
      return; // 如果鼠标移动到了删除图标上，不做任何处理
    }

    // 检查鼠标是否移动到了属性图标上
    if (nodePropertyIcon && (relatedTarget === nodePropertyIcon || nodePropertyIcon.contains(relatedTarget))) {
      return; // 如果鼠标移动到了属性图标上，不做任何处理
    }

    // 如果是容器节点
    if (element.isContainer && element.isResizable) {
      // 如果正在调整大小，不移除句柄
      if (resizingContainer) return;

      // 检查鼠标是否移动到了调整句柄上
      if (resizeHandles.some(handle => handle === relatedTarget || handle.contains(relatedTarget))) {
        return; // 如果鼠标移动到了调整句柄上，不做任何处理
      }

      // 检查是否有任何调整句柄处于鼠标悬停状态
      if (resizeHandles.some(handle => handle._isMouseOver)) {
        return; // 如果有任何调整句柄处于鼠标悬停状态，不移除句柄
      }

      // 延迟隐藏调整大小的句柄，给用户时间移动到句柄上
      setTimeout(() => {
        // 再次检查是否有任何调整句柄处于鼠标悬停状态
        if (resizeHandles.some(handle => handle._isMouseOver)) {
          return; // 如果有任何调整句柄处于鼠标悬停状态，不移除句柄
        }

        // 隐藏调整大小的句柄
        removeResizeHandles();
      }, 100);
    }

    // 如果离开的是当前悬停的节点，隐藏删除图标和锚点
    if (hoveredElement === element) {
      try {
        // 隐藏端口（锚点）
        const ports = elementView.el.querySelectorAll('.joint-port');
        if (ports && ports.length > 0) {
          ports.forEach(port => {
            port.style.opacity = 0;
            port.style.visibility = 'hidden';
          });
        }

        // 另一种方式确保锚点隐藏
        element.getPorts().forEach(port => {
          element.portProp(port.id, 'attrs/circle/opacity', 0);
        });
      } catch (error) {
        console.log('Error hiding ports:', error);
      }

      // 如果是容器节点，确保内部节点和连接线仍然可见
      if (element.isContainer) {
        // 获取容器内的所有节点
        const embeds = element.getEmbeddedCells();
        const embeddedElements = embeds.filter(cell => !cell.isLink());

        // 获取所有相关连接线（包括内部节点之间的连接线）
        const relatedLinks = getAllRelatedLinks(element);

        // 先将容器节点移到前面
        element.toFront({ deep: false });

        // 然后将嵌套节点移到前面
        if (embeddedElements.length > 0) {
          embeddedElements.forEach(embed => {
            embed.toFront();
          });
        }

        // 最后将连接线移到最前面
        if (relatedLinks.length > 0) {
          relatedLinks.forEach(link => {
            link.toFront();
          });
        }

        // 延迟执行，再次确保连接线可见
        setTimeout(() => {
          if (relatedLinks.length > 0) {
            relatedLinks.forEach(link => {
              link.toFront();
            });
          }
        }, 50);
      }

      hoveredElement = null;
      delayHideIcons(); // 使用延迟隐藏而不是立即隐藏
    }
  });

  // 当节点移动时，更新图标位置
  paper.on('element:pointermove', function(elementView, evt, x, y) {
    if (hoveredElement === elementView.model) {
      // 使用requestAnimationFrame来优化性能
      requestAnimationFrame(function() {
        if (nodeDeleteIcon) {
          createNodeDeleteIcon(elementView);
        }
        if (nodePropertyIcon) {
          createNodePropertyIcon(elementView);
        }
      });
    }
  });

  // 当画布缩放或平移时，更新图标位置
  paper.on('scale translate', function() {
    if (hoveredElement) {
      const elementView = paper.findViewByModel(hoveredElement);
      if (elementView) {
        requestAnimationFrame(function() {
          if (nodeDeleteIcon) {
            createNodeDeleteIcon(elementView);
          }
          if (nodePropertyIcon) {
            createNodePropertyIcon(elementView);
          }
        });
      }
    }
  });

  // 点击空白处取消选中
  paper.on('blank:pointerclick', function() {
    hoveredElement = null;
    removeNodeDeleteIcon();
    removeNodePropertyIcon();
    hidePropertyPanel();
  });

  // 监听键盘事件，按Delete或Backspace键删除选中的节点，按ESC键关闭属性面板
  document.addEventListener('keydown', function(evt) {
    // 如果已经在平移模式，不处理其他键盘事件
    if (isPanningMode) {
      return;
    }

    // ESC键关闭属性面板
    if (evt.key === 'Escape') {
      hidePropertyPanel();
      return;
    }

    // 检查当前焦点是否在输入框或文本区域上，如果是则不执行节点删除操作
    const activeElement = document.activeElement;
    const isInputField = activeElement.tagName === 'INPUT' ||
                         activeElement.tagName === 'TEXTAREA' ||
                         activeElement.isContentEditable;

    // 如果焦点在输入字段上，不执行删除节点操作
    if (isInputField) {
      return;
    }

    if ((evt.key === 'Delete' || evt.key === 'Backspace') && hoveredElement) {
      // 保存当前悬停的节点引用，防止在删除过程中被修改
      const elementToDelete = hoveredElement;

      console.log('键盘删除节点:', elementToDelete.id, elementToDelete.get('type'));

      // 立即清除所有图标，防止干扰
      removeAllRelatedIcons();

      // 立即清除悬停状态，防止状态混乱
      hoveredElement = null;

      // 确保移除resize句柄
      removeResizeHandles();

      // 隐藏属性面板
      hidePropertyPanel();

      // 检查节点类型，对特定类型使用增强的删除逻辑
      const nodeType = elementToDelete.get('type');
      const nodeLabel = elementToDelete.attr('label/text');
      const isEndNode = nodeType === 'standard.Circle' && nodeLabel === '结束';
      const isStartNode = nodeType === 'standard.Circle' && nodeLabel === '开始';
      const isContainerNode = elementToDelete.isContainer;

      // 使用与删除图标点击相同的增强删除逻辑
      try {
        // 对于容器节点，需要特殊处理
        if (isContainerNode) {
          // 获取容器中的所有嵌套节点
          const embeddedCells = elementToDelete.getEmbeddedCells();
          console.log(`键盘删除：容器节点 ${elementToDelete.id} 将被删除，包含 ${embeddedCells.length} 个嵌套节点`);

          // 先解除所有嵌套关系
          if (embeddedCells.length > 0) {
            embeddedCells.forEach(cell => {
              try {
                elementToDelete.unembed(cell);
                console.log(`解除嵌套关系: ${cell.id}`);
              } catch (e) {
                console.warn(`解除嵌套关系失败: ${cell.id}`, e);
              }
            });
          }

          // 使用多种方法尝试删除容器节点
          try {
            // 方法1: 直接使用remove方法
            elementToDelete.remove();
            console.log('容器节点删除成功 (方法1)');
          } catch (e1) {
            console.warn('容器节点删除方法1失败:', e1);

            try {
              // 方法2: 使用graph API
              graph.removeCells([elementToDelete]);
              console.log('容器节点删除成功 (方法2)');
            } catch (e2) {
              console.warn('容器节点删除方法2失败:', e2);

              // 方法3: 使用底层collection API
              if (elementToDelete.collection) {
                elementToDelete.collection.remove(elementToDelete);
                console.log('容器节点删除成功 (方法3)');
              } else {
                throw new Error('无法删除容器节点');
              }
            }
          }
        }
        // 对于开始节点，使用简化的删除逻辑
        else if (isStartNode) {
          deleteStartNode(elementToDelete);
        }
        // 对于结束节点，使用增强的删除逻辑
        else if (isEndNode) {
          console.log(`键盘删除：结束节点 ${elementToDelete.id} 将被删除`);

          // 使用多种方法尝试删除结束节点
          try {
            // 方法1: 直接使用remove方法
            elementToDelete.remove();
            console.log('结束节点删除成功 (方法1)');
          } catch (e1) {
            console.warn('结束节点删除方法1失败:', e1);

            try {
              // 方法2: 使用graph API
              graph.removeCells([elementToDelete]);
              console.log('结束节点删除成功 (方法2)');
            } catch (e2) {
              console.warn('结束节点删除方法2失败:', e2);

              // 方法3: 使用底层collection API
              if (elementToDelete.collection) {
                elementToDelete.collection.remove(elementToDelete);
                console.log('结束节点删除成功 (方法3)');
              } else {
                throw new Error('无法删除结束节点');
              }
            }
          }
        }
        // 对于普通节点，直接删除
        else {
          elementToDelete.remove();
        }
      } catch (error) {
        console.error('键盘删除节点时出错:', error);

        // 备用方法 - 简化的删除逻辑
        try {
          console.log('尝试使用备用方法删除节点');
          graph.removeCells([elementToDelete]);
        } catch (e) {
          console.error('键盘备用删除方法也失败:', e);
        }
      }

      // 最后一次确保清理所有图标和句柄
      setTimeout(() => {
        removeAllRelatedIcons();
        removeResizeHandles();
      }, 200);
    }
  });

  // ========== 容器节点分组/嵌套 ========== //
  paper.on('element:pointerup', function(elementView, evt) {
    const el = elementView.model;

    // 容器节点本身不能被嵌套
    if (el.isContainer) {
      return;
    }

    // 检查节点类型，开始和结束节点不能被嵌套
    const nodeType = el.get('type');
    const isStartOrEnd = nodeType === 'standard.Circle' &&
                        (el.attr('label/text') === '开始' || el.attr('label/text') === '结束');

    if (isStartOrEnd) {
      // 如果是开始或结束节点，确保它们不在任何容器中
      const embeddingParent = el.getParentCell();
      if (embeddingParent) {
        embeddingParent.unembed(el);
      }
      return;
    }

    // 判断是否在某个容器内
    const containers = graph.getElements().filter(e => e.isContainer);
    for (const container of containers) {
      const bbox = container.getBBox();
      const elBBox = el.getBBox();
      // 判断节点中心是否在容器内
      const center = elBBox.center();
      if (
        center.x > bbox.x &&
        center.x < bbox.x + bbox.width &&
        center.y > bbox.y &&
        center.y < bbox.y + bbox.height
      ) {
        // 嵌套前，确保节点在容器上方显示
        el.toFront();

        // 嵌套
        container.embed(el);

        // 自动调整节点到容器内部
        const minX = bbox.x + 10; // 添加内边距
        const maxX = bbox.x + bbox.width - elBBox.width - 10;
        const minY = bbox.y + 30; // 顶部留出更多空间给标题
        const maxY = bbox.y + bbox.height - elBBox.height - 10;
        el.position(
          Math.max(minX, Math.min(elBBox.x, maxX)),
          Math.max(minY, Math.min(elBBox.y, maxY))
        );

        // 确保嵌套后节点仍然可见
        setTimeout(() => {
          el.toFront();
        }, 50);

        return;
      } else {
        // 如果之前嵌套过但现在不在容器内，取消嵌套
        if (container.getEmbeddedCells().includes(el)) {
          container.unembed(el);
          // 确保取消嵌套后节点仍然可见
          el.toFront();
        }
      }
    }
  });

  // 确保嵌套在容器中的节点和连接线始终可见
  graph.on('change:position', function(cell) {
    if (cell.isContainer) {
      // 当容器移动时，确保其中的节点和连接线保持可见
      const embeds = cell.getEmbeddedCells();
      const embeddedElements = embeds.filter(embed => !embed.isLink());

      // 获取所有相关连接线（包括内部节点之间的连接线）
      const relatedLinks = getAllRelatedLinks(cell);

      // 先将容器节点移到前面
      cell.toFront({ deep: false });

      // 然后将嵌套节点移到前面
      if (embeddedElements.length > 0) {
        embeddedElements.forEach(embed => {
          // 将嵌套节点移到前面
          embed.toFront();
        });
      }

      // 最后将连接线移到最前面
      if (relatedLinks.length > 0) {
        relatedLinks.forEach(link => {
          link.toFront();
        });
      }

      // 延迟执行，再次确保连接线可见
      setTimeout(() => {
        // 再次确保嵌套节点在容器上方
        if (embeddedElements.length > 0) {
          embeddedElements.forEach(embed => {
            embed.toFront();
          });
        }

        // 最后将连接线移到最前面
        if (relatedLinks.length > 0) {
          relatedLinks.forEach(link => {
            link.toFront();
          });
        }
      }, 50);

      // 如果当前悬停的元素是这个容器，更新resize图标位置
      if (hoveredElement === cell) {
        const cellView = paper.findViewByModel(cell);
        if (cellView) {
          // 立即更新一次
          if (cell.isResizable) {
            removeResizeHandles();
            createResizeHandles(cellView);
          }

          // 更新图标
          if (nodeDeleteIcon) {
            createNodeDeleteIcon(cellView);
          }
          if (nodePropertyIcon) {
            createNodePropertyIcon(cellView);
          }
        }
      }
    }
  });



  // 监听容器节点的点击事件，确保内部节点和连接线始终可见
  paper.on('element:pointerdown', function(elementView, evt) {
    const element = elementView.model;

    // 如果点击的是容器节点
    if (element.isContainer) {
      // 获取容器内的所有节点
      const embeds = element.getEmbeddedCells();
      const embeddedElements = embeds.filter(cell => !cell.isLink());

      // 获取所有相关连接线（包括内部节点之间的连接线）
      const relatedLinks = getAllRelatedLinks(element);

      // 立即执行一次，确保连接线可见
      // 先将容器节点移到前面
      element.toFront({ deep: false });

      // 然后将嵌套节点移到前面
      if (embeddedElements.length > 0) {
        embeddedElements.forEach(embed => {
          embed.toFront();
        });
      }

      // 最后将连接线移到最前面
      if (relatedLinks.length > 0) {
        relatedLinks.forEach(link => {
          link.toFront();
        });
      }

      // 延迟执行，再次确保在容器被选中后内部节点和连接线仍然可见
      setTimeout(() => {
        // 先将容器节点移到前面
        element.toFront({ deep: false });

        // 然后将嵌套节点移到前面
        if (embeddedElements.length > 0) {
          embeddedElements.forEach(embed => {
            embed.toFront();
          });
        }

        // 最后将连接线移到最前面
        if (relatedLinks.length > 0) {
          relatedLinks.forEach(link => {
            link.toFront();
          });
        }
      }, 50);
    }
  });

  // 监听容器节点的点击释放事件，再次确保内部节点和连接线可见
  paper.on('element:pointerup', function(elementView, evt) {
    const element = elementView.model;

    // 如果点击的是容器节点
    if (element.isContainer) {
      // 获取容器内的所有节点
      const embeds = element.getEmbeddedCells();
      const embeddedElements = embeds.filter(cell => !cell.isLink());

      // 获取所有相关连接线（包括内部节点之间的连接线）
      const relatedLinks = getAllRelatedLinks(element);

      // 先将容器节点移到前面
      element.toFront({ deep: false });

      // 然后将嵌套节点移到前面
      if (embeddedElements.length > 0) {
        embeddedElements.forEach(embed => {
          embed.toFront();
        });
      }

      // 最后将连接线移到最前面
      if (relatedLinks.length > 0) {
        relatedLinks.forEach(link => {
          link.toFront();
        });
      }

      // 延迟执行，再次确保连接线可见
      setTimeout(() => {
        if (relatedLinks.length > 0) {
          relatedLinks.forEach(link => {
            link.toFront();
          });
        }
      }, 100);
    }
  });

  // 监听节点删除事件，确保清理resize句柄和图标 - 增强版本
  graph.on('remove', function(cell) {
    try {
      console.log(`节点被删除: ${cell.id}, 类型: ${cell.get('type')}`);

      // 立即清除悬停状态，防止状态混乱
      if (hoveredElement === cell) {
        hoveredElement = null;
      }

      // 对所有节点类型进行处理，确保清理所有相关元素
      let nodeType, nodeLabel;
      try {
        nodeType = cell.get('type');
        nodeLabel = cell.attr('label/text');
      } catch (e) {
        console.warn('获取节点类型或标签失败:', e);
        nodeType = 'unknown';
        nodeLabel = '';
      }

      // 如果删除的是容器节点，确保移除resize句柄和所有相关图标
      if (cell.isContainer) {
        try {
          // 获取容器中的所有嵌套节点（在删除前）
          const embeddedCells = cell.getEmbeddedCells ? cell.getEmbeddedCells() : [];

          // 记录嵌套节点的ID，用于后续清理
          const embeddedIds = embeddedCells.map(embedded => embedded.id);

          console.log(`容器节点 ${cell.id} 被删除，包含 ${embeddedCells.length} 个嵌套节点`);
        } catch (e) {
          console.warn('获取嵌套节点失败:', e);
        }

        // 移除resize句柄
        removeResizeHandles();
      }
      // 如果是开始节点，特殊处理
      else if (nodeType === 'standard.Circle' && nodeLabel === '开始') {
        console.log(`开始节点被删除: ${cell.id}`);
      }
      // 如果是结束节点，特殊处理
      else if (nodeType === 'standard.Circle' && nodeLabel === '结束') {
        console.log(`结束节点被删除: ${cell.id}`);
      }
      // 如果是Grouping节点或Decision节点，特殊处理
      else if ((nodeType === 'standard.Rectangle' && !cell.isContainer && !cell.isSwitch) ||
               nodeType === 'standard.Polygon') {
        console.log(`特殊节点被删除: ${cell.id}, 类型: ${nodeType}`);
      }

      // 确保所有相关图标都被移除
      removeAllRelatedIcons();

      // 多次延迟执行，确保所有图标和句柄都被彻底清理
      const cleanupIntervals = [50, 150, 300];
      cleanupIntervals.forEach(delay => {
        setTimeout(() => {
          // 再次移除所有图标和句柄
          removeAllRelatedIcons();
          removeResizeHandles();

          // 确保没有残留的图标
          const allIcons = document.querySelectorAll('.node-delete-icon, .node-property-icon');
          if (allIcons.length > 0) {
            console.log(`删除节点后 ${delay}ms 仍有 ${allIcons.length} 个图标残留，强制清理`);
            allIcons.forEach(icon => {
              try {
                if (icon.parentNode) {
                  // 禁用事件处理，防止在删除过程中触发事件
                  icon.style.pointerEvents = 'none';
                  icon.parentNode.removeChild(icon);
                }
              } catch (e) {
                console.warn('移除残留图标失败:', e);
              }
            });
          }

          // 确保没有残留的resize句柄
          const allHandles = document.querySelectorAll('.resize-handle');
          if (allHandles.length > 0) {
            console.log(`删除节点后 ${delay}ms 仍有 ${allHandles.length} 个resize句柄残留，强制清理`);
            allHandles.forEach(handle => {
              try {
                if (handle.parentNode) {
                  handle.parentNode.removeChild(handle);
                }
              } catch (e) {
                console.warn('移除残留resize句柄失败:', e);
              }
            });
          }
        }, delay);
      });

      // 如果删除的是当前悬停的节点，清除引用并移除图标
      if (hoveredElement === cell) {
        hoveredElement = null;
        removeNodeDeleteIcon();
        removeNodePropertyIcon();
      }

      // 如果删除的是当前正在编辑属性的节点，隐藏属性面板
      if (currentEditingNode === cell) {
        hidePropertyPanel();
      }
    } catch (error) {
      console.error('节点删除事件处理出错:', error);

      // 出错时也要确保清理所有图标和句柄
      try {
        removeAllRelatedIcons();
        removeResizeHandles();

        // 重置状态
        hoveredElement = null;
        hidePropertyPanel();
      } catch (e) {
        console.error('清理失败:', e);
      }
    }
  });

  // 监听连接线创建事件
  paper.on('link:connect', function(linkView) {
    const link = linkView.model;
    console.log('连接线已创建:', link.id);

    // 检查源节点是否是 Switch 节点
    const sourceCell = link.getSourceCell();
    if (sourceCell && sourceCell.isSwitch) {
      // 获取源端口
      const sourcePort = link.getSourcePort();
      if (sourcePort) {
        // 从端口 ID 中提取 case 索引
        const caseIndexMatch = sourcePort.match(/case_(\d+)/);
        if (caseIndexMatch && caseIndexMatch[1]) {
          const caseIndex = parseInt(caseIndexMatch[1]);

          // 获取 case 名称
          const cases = sourceCell.prop('properties').cases || [];
          if (cases[caseIndex]) {
            const caseName = cases[caseIndex].name;

            // 在连接线上添加标签
            link.appendLabel({
              attrs: {
                text: {
                  text: caseName,
                  fill: '#333',
                  fontSize: 12,
                  fontWeight: 'bold',
                  textAnchor: 'middle',
                  textVerticalAnchor: 'middle',
                  pointerEvents: 'none'
                },
                rect: {
                  fill: 'white',
                  stroke: '#ccc',
                  strokeWidth: 1,
                  rx: 3,
                  ry: 3,
                  refWidth: '100%',
                  refHeight: '100%',
                  refX: 0,
                  refY: 0
                }
              },
              position: {
                distance: 0.5 // 在连接线中间位置
              }
            });
          }
        }
      }
    }
  });

  // ========== 缩放控制功能 ========== //

  // 缩放相关变量
  let zoomLevel = 1; // 当前缩放比例
  const MIN_SCALE = 0.3; // 最小缩放比例 (30%)
  const MAX_SCALE = 2.0; // 最大缩放比例 (200%)
  const SCALE_STEP = 0.1; // 缩放步长 (10%)

  // 缩放控制元素
  const zoomToolbar = document.getElementById('zoom-toolbar');
  const zoomInBtn = document.getElementById('zoom-in');
  const zoomOutBtn = document.getElementById('zoom-out');
  const zoomPercentage = document.getElementById('zoom-percentage');
  const toggleMinimapBtn = document.getElementById('toggle-minimap');
  // 使用已经定义的minimapContainer变量，不再重复声明

  // 从本地存储中获取小地图显示状态
  const isMinimapVisible = localStorage.getItem('minimapVisible') !== 'false'; // 默认显示

  // 初始化小地图显示状态
  if (!isMinimapVisible) {
    minimapContainer.style.display = 'none';
    minimapContainer.style.opacity = '0';
  } else {
    minimapContainer.style.display = 'block';
    minimapContainer.style.opacity = '0.7';
  }

  // 更新缩放百分比显示
  function updateZoomPercentage() {
    zoomPercentage.textContent = Math.round(zoomLevel * 100) + '%';
  }

  // 点击缩放百分比重置为100%
  zoomPercentage.addEventListener('click', function() {
    zoomPaper(1); // 重置为100%
  });

  // 执行缩放操作
  function zoomPaper(newScale) {
    // 限制缩放范围
    newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, newScale));

    if (newScale === zoomLevel) return; // 如果缩放比例没有变化，不执行操作

    // 获取当前视口中心点
    const clientRect = paper.el.getBoundingClientRect();
    const centerX = clientRect.width / 2;
    const centerY = clientRect.height / 2;

    // 获取当前平移和缩放
    const currentTranslate = paper.translate();
    const oldScale = paper.scale().sx;

    // 计算中心点在模型坐标系中的位置
    const modelCenterX = (centerX - currentTranslate.tx) / oldScale;
    const modelCenterY = (centerY - currentTranslate.ty) / oldScale;

    // 计算新的平移值，保持视口中心点不变
    const newTx = centerX - modelCenterX * newScale;
    const newTy = centerY - modelCenterY * newScale;

    // 应用新的缩放和平移
    paper.scale(newScale, newScale);
    paper.translate(newTx, newTy);

    // 更新当前缩放比例
    zoomLevel = newScale;

    // 更新缩放百分比显示
    updateZoomPercentage();

    // 更新小地图视口
    updateMinimapViewport();
  }

  // 放大按钮点击事件
  zoomInBtn.addEventListener('click', function() {
    zoomPaper(zoomLevel + SCALE_STEP);
  });

  // 缩小按钮点击事件
  zoomOutBtn.addEventListener('click', function() {
    zoomPaper(zoomLevel - SCALE_STEP);
  });

  // 切换小地图显示/隐藏
  toggleMinimapBtn.addEventListener('click', function() {
    // 获取当前显示状态
    const isVisible = minimapContainer.style.display !== 'none';

    // 切换显示状态
    if (isVisible) {
      // 淡出动画
      minimapContainer.style.opacity = '0';
      setTimeout(() => {
        minimapContainer.style.display = 'none';
      }, 300);
    } else {
      // 淡入动画
      minimapContainer.style.display = 'block';
      minimapContainer.style.opacity = '0';
      setTimeout(() => {
        minimapContainer.style.opacity = '0.7';
      }, 10);
    }

    // 保存状态到本地存储
    localStorage.setItem('minimapVisible', !isVisible);
  });

  // 键盘快捷键支持
  document.addEventListener('keydown', function(evt) {
    // 如果按下了Ctrl键
    if (evt.ctrlKey || evt.metaKey) {
      // Ctrl++ 放大
      if (evt.key === '+' || evt.key === '=') {
        evt.preventDefault(); // 阻止默认行为
        zoomPaper(zoomLevel + SCALE_STEP);
      }
      // Ctrl+- 缩小
      else if (evt.key === '-') {
        evt.preventDefault(); // 阻止默认行为
        zoomPaper(zoomLevel - SCALE_STEP);
      }
      // Ctrl+0 重置缩放
      else if (evt.key === '0') {
        evt.preventDefault(); // 阻止默认行为
        zoomPaper(1); // 重置为100%
      }
      // Ctrl+M 切换小地图
      else if (evt.key === 'm' || evt.key === 'M') {
        evt.preventDefault(); // 阻止默认行为
        toggleMinimapBtn.click(); // 触发小地图切换按钮的点击事件
      }
    }
  });

  // 鼠标滚轮缩放支持
  paper.el.addEventListener('wheel', function(evt) {
    // 如果按下了Ctrl键，执行缩放
    if (evt.ctrlKey || evt.metaKey) {
      evt.preventDefault(); // 阻止默认的滚动行为

      // 根据滚轮方向确定缩放方向
      const delta = evt.deltaY || evt.detail || evt.wheelDelta;

      if (delta < 0) {
        // 向上滚动，放大
        zoomPaper(zoomLevel + SCALE_STEP);
      } else {
        // 向下滚动，缩小
        zoomPaper(zoomLevel - SCALE_STEP);
      }
    }
  }, { passive: false }); // passive: false 允许阻止默认行为

  // 窗口大小变化时调整工具栏位置
  window.addEventListener('resize', function() {
    // 清除之前的定时器
    clearTimeout(this.resizeToolbarTimeout);

    // 设置新的定时器，延迟执行以防止频繁调用
    this.resizeToolbarTimeout = setTimeout(function() {
      // 根据窗口大小调整工具栏位置
      const windowWidth = window.innerWidth;

      if (windowWidth < 768) {
        // 小屏幕上调整位置
        zoomToolbar.style.right = '210px';
      } else {
        // 大屏幕上恢复默认位置
        zoomToolbar.style.right = '250px';
      }
    }, 100);
  });

  // 初始化缩放百分比显示
  updateZoomPercentage();
